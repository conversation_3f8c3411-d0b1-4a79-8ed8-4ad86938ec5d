# Translation System Guide - French/Arabic Support

This guide explains how to use the complete French/Arabic translation system implemented in your Sademy project.

## 🌍 Features

- **Bilingual Support**: French (default) and Arabic
- **RTL Support**: Full right-to-left layout support for Arabic
- **Language Switcher**: Easy language switching in the header
- **Persistent Language**: Language preference saved in localStorage
- **RTL-Aware Animations**: Animations that adapt to text direction
- **Arabic Font Support**: Noto Sans Arabic font integration

## 📁 File Structure

```
src/
├── i18n/
│   ├── config.ts          # Language configuration
│   ├── context.tsx        # Translation context and provider
│   └── translations.ts    # Translation files
├── components/
│   ├── Header/
│   │   └── LanguageSwitcher.tsx  # Language switcher component
│   └── Contact/
│       └── index.tsx      # Example translated component
└── styles/
    └── index.css          # RTL styles and Arabic font support
```

## 🚀 Usage

### 1. Using Translations in Components

```tsx
"use client";
import { useI18n } from "@/i18n/context";

const MyComponent = () => {
  const { t, locale, isRTL } = useI18n();

  return (
    <div className={isRTL ? 'text-right' : 'text-left'}>
      <h1>{t('contact.title')}</h1>
      <p>{t('contact.description')}</p>
    </div>
  );
};
```

### 2. Available Translation Keys

Current translations include:

```typescript
// French (fr) and Arabic (ar) translations
contact: {
  title: "Contact title"
  description: "Contact description"
  form: {
    name: { label: "Name", placeholder: "Enter name" }
    email: { label: "Email", placeholder: "Enter email" }
    message: { label: "Message", placeholder: "Enter message" }
    submit: "Submit button text"
  }
}
```

### 3. Adding New Translations

Edit `src/i18n/translations.ts`:

```typescript
export const translations: Record<Locale, any> = {
  fr: {
    // Add French translations
    newSection: {
      title: "Nouveau Titre",
      description: "Nouvelle Description"
    }
  },
  ar: {
    // Add Arabic translations
    newSection: {
      title: "عنوان جديد",
      description: "وصف جديد"
    }
  }
};
```

## 🎨 RTL Styling

### Automatic RTL Support

The system automatically:
- Sets `dir="rtl"` on the document for Arabic
- Applies Arabic font (Noto Sans Arabic)
- Reverses spacing and margins
- Adjusts text alignment

### Custom RTL Styles

Use RTL-specific classes in your CSS:

```css
[dir="rtl"] .my-component {
  text-align: right;
  margin-right: 0;
  margin-left: auto;
}
```

## 🔧 Configuration

### Language Configuration (`src/i18n/config.ts`)

```typescript
export const locales = ['fr', 'ar'] as const;
export const defaultLocale: Locale = 'fr';

export const localeNames: Record<Locale, string> = {
  fr: 'Français',
  ar: 'العربية'
};
```

### Adding New Languages

1. Add the locale to `locales` array in `config.ts`
2. Add translations in `translations.ts`
3. Add locale name and flag in `config.ts`
4. Update RTL logic if needed

## 🎯 Language Switcher

The language switcher in the header allows users to:
- Switch between French and Arabic
- See current language with flag
- Persist language choice in localStorage

## 📱 Responsive Design

The translation system is fully responsive:
- Language switcher adapts to screen size
- RTL layouts work on all devices
- Arabic text flows correctly on mobile

## 🧪 Testing

Visit these URLs to test the translation system:
- Main page: `http://localhost:3000`
- Translation demo: `http://localhost:3000/translation-demo`

## 🔍 Example: Contact Component

The Contact component demonstrates complete translation integration:

```tsx
const Contact = () => {
  const { t, isRTL } = useI18n();

  return (
    <section className={isRTL ? 'text-right' : 'text-left'}>
      <h2>{t('contact.title')}</h2>
      <p>{t('contact.description')}</p>
      <form>
        <input placeholder={t('contact.form.name.placeholder')} />
        <button>{t('contact.form.submit')}</button>
      </form>
    </section>
  );
};
```

## 🎨 Styling Best Practices

1. **Use RTL-aware classes**: Apply `isRTL` conditionally
2. **Test both directions**: Always test in both French and Arabic
3. **Consider text length**: Arabic text may be longer/shorter
4. **Use semantic spacing**: Prefer logical properties when possible

## 🚀 Next Steps

To extend the translation system:

1. **Add more components**: Translate Header, Footer, Hero sections
2. **Add more languages**: Extend to English, Spanish, etc.
3. **Add pluralization**: Implement plural forms for complex translations
4. **Add date/number formatting**: Locale-specific formatting

## 📝 Notes

- Language preference persists across browser sessions
- RTL animations automatically reverse for Arabic
- All form inputs support RTL text input
- The system is optimized for performance with minimal re-renders
