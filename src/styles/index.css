@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap')
layer(base);

/* Arabic font support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap')
layer(base);

@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --breakpoint-*: initial;
  --breakpoint-xs: 450px;
  --breakpoint-sm: 575px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;

  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #121723;
  --color-dark: #1d2430;
  --color-primary: #1CBCCF;
  --color-secondary: #2E86C1;
  --color-yellow: #fbb040;
  --color-bg-color-dark: #171c28;

  --color-body-color: #788293;
  --color-body-color-dark: #959cb1;

  --color-stroke-stroke: #e3e8ef;
  --color-stroke-dark: #353943;

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
  --color-gray-dark: #1e232e;
  --color-gray-light: #f0f2f9;

  --shadow-sign-up: 0px 5px 10px rgba(4, 10, 34, 0.2);
  --shadow-one: 0px 2px 3px rgba(7, 7, 77, 0.05);
  --shadow-two: 0px 5px 10px rgba(6, 8, 15, 0.1);
  --shadow-three: 0px 5px 15px rgba(6, 8, 15, 0.05);

  /* Animations personnalisées */
  --animate-fade-in-up: fade-in-up 0.6s ease-out forwards;
  --animate-fade-in-left: fade-in-left 0.6s ease-out forwards;
  --animate-fade-in-right: fade-in-right 0.6s ease-out forwards;
  --animate-scale-in: scale-in 0.4s ease-out forwards;
  --shadow-sticky: inset 0 -1px 0 0 rgba(0, 0, 0, 0.1);
  --shadow-sticky-dark: inset 0 -1px 0 0 rgba(255, 255, 255, 0.1);
  --shadow-feature-2: 0px 10px 40px rgba(48, 86, 211, 0.12);
  --shadow-submit: 0px 5px 20px rgba(4, 10, 34, 0.1);
  --shadow-submit-dark: 0px 5px 20px rgba(4, 10, 34, 0.1);
  --shadow-btn: 0px 1px 2px rgba(4, 10, 34, 0.15);
  --shadow-btn-hover: 0px 1px 2px rgba(0, 0, 0, 0.15);
  --shadow-btn-light: 0px 1px 2px rgba(0, 0, 0, 0.1);

  --drop-shadow-three: 0px 5px 15px rgba(6, 8, 15, 0.05);
}

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;

  /* Mobile-first responsive padding */
  @media (min-width: 450px) {
    padding-inline: 1.5rem;
  }

  @media (min-width: 768px) {
    padding-inline: 2rem;
  }

  @media (min-width: 1200px) {
    padding-inline: 1rem;
  }
}

/* Enhanced mobile-first utilities */
@utility mobile-container {
  margin-inline: auto;
  padding-inline: 1rem;
  max-width: 100%;

  @media (min-width: 450px) {
    padding-inline: 1.5rem;
  }

  @media (min-width: 768px) {
    padding-inline: 2rem;
    max-width: 768px;
  }

  @media (min-width: 992px) {
    max-width: 992px;
  }

  @media (min-width: 1200px) {
    max-width: 1200px;
    padding-inline: 1rem;
  }

  @media (min-width: 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility sticky {
  & .header-logo {
    @apply py-5 lg:py-2;
  }

  & .menu-scroll.active {
    @apply opacity-70;
  }
}

@utility header-logo {
  .sticky & {
    @apply py-5 lg:py-2;
  }
}

@utility menu-scroll {
  .sticky &.active {
    @apply opacity-70;
  }
}

@utility active {
  .sticky &.menu-scroll {
    @apply opacity-70;
  }
}

@utility dot {
  input#togglePlan:checked ~ & {
    @apply translate-x-full;
  }
}

@utility box {
  input#checkboxLabel:checked ~ & span {
    @apply opacity-100;
  }
}

@layer base {
  body {
    font-family: "Inter", sans-serif;
  }

  /* RTL Support */
  [dir="rtl"] {
    font-family: "Noto Sans Arabic", "Inter", sans-serif;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  [dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  [dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
}

@layer components {
  input[type="checkbox"]:checked ~ label span svg {
    @apply inline-flex;
  }

  /* Premium Services Animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(2rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shine {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(28, 188, 207, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(28, 188, 207, 0.6);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animate-shine {
    animation: shine 0.6s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in-up,
    .animate-shine,
    .animate-float,
    .animate-glow,
    .animate-pulse,
    .animate-bounce,
    .animate-ping {
      animation: none;
    }

    * {
      transition-duration: 0.01ms !important;
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
    }
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  /* Enhanced mobile-optimized animations for Sademy */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Mobile-optimized animation classes */
  .animate-fade-in-left {
    animation: fade-in-left 0.6s ease-out forwards;
  }

  .animate-fade-in-right {
    animation: fade-in-right 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out forwards;
  }

  /* Staggered animation delays for mobile */
  .animation-delay-100 { animation-delay: 100ms; }
  .animation-delay-200 { animation-delay: 200ms; }
  .animation-delay-300 { animation-delay: 300ms; }
  .animation-delay-400 { animation-delay: 400ms; }
  .animation-delay-500 { animation-delay: 500ms; }
  .animation-delay-600 { animation-delay: 600ms; }
  .animation-delay-700 { animation-delay: 700ms; }
  .animation-delay-800 { animation-delay: 800ms; }

  /* Mobile-first responsive utilities */
  .mobile-text-center {
    text-align: center;

    @media (min-width: 768px) {
      text-align: left;
    }
  }

  .mobile-flex-col {
    display: flex;
    flex-direction: column;

    @media (min-width: 768px) {
      flex-direction: row;
    }
  }

  .mobile-w-full {
    width: 100%;

    @media (min-width: 768px) {
      width: auto;
    }
  }

  .mobile-mb-4 {
    margin-bottom: 1rem;

    @media (min-width: 768px) {
      margin-bottom: 0;
    }
  }

  .mobile-px-4 {
    padding-left: 1rem;
    padding-right: 1rem;

    @media (min-width: 768px) {
      padding-left: 0;
      padding-right: 0;
    }
  }

  /* Touch-friendly button sizing for mobile */
  .mobile-touch-button {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1.5rem;

    @media (min-width: 768px) {
      min-height: auto;
      min-width: auto;
      padding: 0.5rem 1rem;
    }
  }

  /* Mobile-optimized spacing */
  .mobile-section-padding {
    padding-top: 3rem;
    padding-bottom: 3rem;

    @media (min-width: 768px) {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    @media (min-width: 1024px) {
      padding-top: 5rem;
      padding-bottom: 5rem;
    }
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Optimize animations for mobile performance */
  @media (max-width: 767px) {
    * {
      animation-duration: 0.3s !important;
      transition-duration: 0.3s !important;
    }

    .animate-fade-in-up,
    .animate-fade-in-left,
    .animate-fade-in-right,
    .animate-scale-in,
    .animate-slide-up {
      animation-duration: 0.4s;
    }
  }

  @keyframes fade-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Classes d'animation */
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  .animate-fade-in-left {
    animation: fade-in-left 0.6s ease-out forwards;
  }

  .animate-fade-in-right {
    animation: fade-in-right 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  /* Délais d'animation */
  .animation-delay-200 {
    animation-delay: 0.2s;
    opacity: 0;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
    opacity: 0;
  }

  .animation-delay-600 {
    animation-delay: 0.6s;
    opacity: 0;
  }

  .animation-delay-800 {
    animation-delay: 0.8s;
    opacity: 0;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
    opacity: 0;
  }

  /* PowerPoint Catalog Specific Animations */
  .animate-catalog-item {
    animation: catalog-item-entrance 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  .animate-filter-button {
    animation: filter-button-pop 0.3s ease-out forwards;
  }

  .animate-modal-backdrop {
    animation: modal-backdrop-fade 0.3s ease-out forwards;
  }

  .animate-modal-content {
    animation: modal-content-scale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }

  /* Keyframes for catalog animations */
  @keyframes catalog-item-entrance {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes filter-button-pop {
    0% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes modal-backdrop-fade {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes modal-content-scale {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Effets hover améliorés */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Gradient text utilities */
  .gradient-text {
    background: linear-gradient(135deg, #1CBCCF, #2E86C1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Catalog-specific utilities */
  .catalog-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .catalog-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .catalog-overlay {
    background: linear-gradient(135deg, rgba(28, 188, 207, 0.9), rgba(46, 134, 193, 0.9));
    backdrop-filter: blur(10px);
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Glassmorphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Mobile-specific utilities */
  .mobile-touch-button {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  .mobile-touch-input {
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Mobile optimizations */
  @media (max-width: 767px) {
    .catalog-card:hover {
      transform: none;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .animate-catalog-item {
      animation-duration: 0.4s;
    }

    .animate-modal-content {
      animation-duration: 0.3s;
    }

    /* Reduce motion for better mobile performance */
    .catalog-overlay {
      backdrop-filter: blur(5px);
    }

    /* Touch-friendly spacing */
    .mobile-grid-gap {
      gap: 1rem;
    }

    /* Optimize text for mobile reading */
    .mobile-text-optimize {
      line-height: 1.6;
      font-size: 0.875rem;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 768px) and (max-width: 1023px) {
    .catalog-card:hover {
      transform: translateY(-4px) scale(1.01);
    }
  }

  /* RTL-specific animations */
  @keyframes fade-in-right-rtl {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-left-rtl {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  [dir="rtl"] .animate-fade-in-left {
    animation: fade-in-right-rtl 0.6s ease-out forwards;
  }

  [dir="rtl"] .animate-fade-in-right {
    animation: fade-in-left-rtl 0.6s ease-out forwards;
  }

  /* RTL form styles */
  [dir="rtl"] input,
  [dir="rtl"] textarea {
    text-align: right;
  }

  [dir="rtl"] .placeholder\:text-right::placeholder {
    text-align: right;
  }

  /* Feature cards animations and styling */
  @keyframes float-subtle {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .feature-card-float {
    animation: float-subtle 4s ease-in-out infinite;
  }

  .feature-card-shadow {
    box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
  }

  .feature-card-shadow:hover {
    box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.15);
    transform: translateY(-8px);
  }

  /* Enhanced card styling for features */
  .feature-icon-pulse {
    position: relative;
  }

  .feature-icon-pulse::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(28, 188, 207, 0.3) 0%, rgba(28, 188, 207, 0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .feature-icon-pulse:hover::after {
    opacity: 1;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Responsive improvements for feature cards */
  @media (max-width: 640px) {
    .feature-card {
      margin-bottom: 1.5rem;
    }
    
    .feature-icon {
      width: 3.5rem;
      height: 3.5rem;
    }
  }

  /* Dark mode enhancements for feature cards */
  .dark .feature-card {
    background-color: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    border-color: rgba(55, 65, 81, 0.5);
  }

  /* Admin Dashboard Modern Enhancements */
  .admin-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .admin-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .admin-button-glow {
    position: relative;
    overflow: hidden;
  }

  .admin-button-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .admin-button-glow:hover::before {
    left: 100%;
  }

  /* Glass morphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Animated gradient backgrounds */
  .gradient-animation {
    background: linear-gradient(-45deg, #1CBCCF, #2E86C1, #1CBCCF, #2E86C1);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Modern shadow utilities */
  .shadow-modern {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-modern-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-modern-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .dark .feature-card:hover {
    background-color: rgba(31, 41, 55, 0.95);
    border-color: rgba(28, 188, 207, 0.3);
  }
}
