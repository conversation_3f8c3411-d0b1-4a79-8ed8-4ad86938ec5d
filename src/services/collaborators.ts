import api from './api';

// Collaborator interfaces
export interface Collaborator {
  id: number;
  name: string;
  email: string;
  phone: string;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface CollaboratorResponse {
  success: boolean;
  data: {
    collaborators: Collaborator[];
    pagination?: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
      from: number;
      to: number;
    };
  };
}

export interface CreateCollaboratorRequest {
  name: string;
  email: string;
  phone: string;
}

export interface UpdateCollaboratorRequest {
  name?: string;
  email?: string;
  phone?: string;
  is_active?: boolean;
}

export interface CollaboratorFilters {
  search?: string;
  is_active?: boolean;
  page?: number;
  per_page?: number;
}

export class CollaboratorService {
  /**
   * Get all collaborators with optional filters (Admin endpoint)
   */
  static async getCollaborators(filters?: CollaboratorFilters): Promise<CollaboratorResponse> {
    try {
      const params = new URLSearchParams();

      if (filters?.search) params.append('search', filters.search);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.per_page) params.append('per_page', filters.per_page.toString());

      const queryString = params.toString();
      const url = queryString ? `/admin/collaborators?${queryString}` : '/admin/collaborators';
      
      const response = await api.get<CollaboratorResponse>(url);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des collaborateurs');
    }
  }

  /**
   * Get a single collaborator by ID (Admin endpoint)
   */
  static async getCollaborator(id: number): Promise<Collaborator> {
    try {
      const response = await api.get<{ success: boolean; data: { collaborator: Collaborator } }>(`/admin/collaborators/${id}`);
      return response.data.data.collaborator;
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Collaborateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du collaborateur');
    }
  }

  /**
   * Create a new collaborator (Admin only)
   */
  static async createCollaborator(data: CreateCollaboratorRequest): Promise<Collaborator> {
    try {
      const response = await api.post<{ success: boolean; data: { collaborator: Collaborator } }>('/admin/add-collaborator', data);
      return response.data.data.collaborator;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent ajouter des collaborateurs.');
      }
      if (error.response?.status === 422) {
        const errors = error.response.data.errors;
        if (errors) {
          const errorMessages = Object.values(errors).flat();
          throw new Error(errorMessages.join(', '));
        }
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la création du collaborateur');
    }
  }

  /**
   * Update a collaborator (Admin only)
   */
  static async updateCollaborator(id: number, data: UpdateCollaboratorRequest): Promise<Collaborator> {
    try {
      const response = await api.put<{ success: boolean; data: { collaborator: Collaborator } }>(`/admin/collaborators/${id}`, data);
      return response.data.data.collaborator;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent modifier des collaborateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Collaborateur non trouvé');
      }
      if (error.response?.status === 422) {
        const errors = error.response.data.errors;
        if (errors) {
          const errorMessages = Object.values(errors).flat();
          throw new Error(errorMessages.join(', '));
        }
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du collaborateur');
    }
  }

  /**
   * Delete a collaborator (Admin only)
   */
  static async deleteCollaborator(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.delete<{ success: boolean; message: string }>(`/admin/collaborators/${id}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent supprimer des collaborateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Collaborateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression du collaborateur');
    }
  }

  /**
   * Toggle collaborator active status (Admin only)
   */
  static async toggleCollaboratorStatus(id: number): Promise<Collaborator> {
    try {
      const response = await api.patch<{ success: boolean; data: { collaborator: Collaborator } }>(`/admin/collaborators/${id}/toggle-status`);
      return response.data.data.collaborator;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent modifier le statut des collaborateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Collaborateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification du statut du collaborateur');
    }
  }
}
