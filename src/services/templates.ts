import api from './api';

// Template interfaces based on the new admin API
export interface Template {
  id: number;
  title: string;
  description: string;
  file_type: string;
  presentation_file?: string;
  presentation_file_url?: string;
  demo_video?: string;
  demo_video_url?: string;
  has_demo_video?: boolean;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface TemplateResponse {
  success: boolean;
  data: {
    templates: Template[];
    pagination: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
      from: number;
      to: number;
    };
  };
}

export interface CreateTemplateRequest {
  title: string;
  description: string;
  file_type: string;
  presentation_file: File;
  demo_video?: File;
}

export interface CreateTemplateResponse {
  success: boolean;
  message: string;
  data: {
    template: Template;
    file_info: {
      presentation_file_size: string;
      demo_video_size?: string;
      storage_path: string;
    };
  };
}

export interface UpdateTemplateRequest {
  title?: string;
  description?: string;
  file_type?: string;
  presentation_file?: File;
  demo_video?: File;
}

export interface TemplateFilters {
  file_type?: string;
  is_active?: boolean;
  search?: string;
  page?: number;
  per_page?: number;
}

export class TemplateService {
  /**
   * Get all templates from public endpoint (no authentication required)
   */
  static async getPublicTemplates(filters?: TemplateFilters): Promise<TemplateResponse> {
    try {
      const params = new URLSearchParams();

      if (filters?.file_type) params.append('file_type', filters.file_type);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      if (filters?.search) params.append('search', filters.search);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.per_page) params.append('per_page', filters.per_page.toString());

      const queryString = params.toString();
      const url = queryString ? `/templates?${queryString}` : '/templates';

      const response = await api.get<TemplateResponse>(url);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des templates');
    }
  }

  /**
   * Get all templates with optional filters (Admin endpoint)
   */
  static async getTemplates(filters?: TemplateFilters): Promise<TemplateResponse> {
    try {
      const params = new URLSearchParams();

      if (filters?.file_type) params.append('file_type', filters.file_type);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      if (filters?.search) params.append('search', filters.search);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.per_page) params.append('per_page', filters.per_page.toString());

      const queryString = params.toString();
      const url = queryString ? `/admin/templates?${queryString}` : '/admin/templates';

      const response = await api.get<TemplateResponse>(url);
      return response.data;
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des templates');
    }
  }

  /**
   * Get a single template by ID (Admin endpoint)
   */
  static async getTemplate(id: number): Promise<Template> {
    try {
      const response = await api.get<{ success: boolean; data: { template: Template } }>(`/admin/templates/${id}`);
      return response.data.data.template;
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Template non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du template');
    }
  }

  /**
   * Create a new template (Admin only)
   */
  static async createTemplate(templateData: CreateTemplateRequest): Promise<CreateTemplateResponse> {
    try {
      // Debug: Log the file being uploaded
      console.log('🔍 Debug - Template Data:', {
        title: templateData.title,
        description: templateData.description,
        file_type: templateData.file_type,
        presentation_file: templateData.presentation_file ? {
          name: templateData.presentation_file.name,
          size: templateData.presentation_file.size,
          type: templateData.presentation_file.type,
          lastModified: templateData.presentation_file.lastModified
        } : 'NO FILE',
        demo_video: templateData.demo_video ? {
          name: templateData.demo_video.name,
          size: templateData.demo_video.size,
          type: templateData.demo_video.type
        } : 'NO VIDEO'
      });

      // Validate file exists
      if (!templateData.presentation_file) {
        throw new Error('Presentation file is required but not provided');
      }

      // Create FormData for multipart/form-data matching the API example
      const formData = new FormData();
      formData.append('title', templateData.title);
      formData.append('description', templateData.description);
      formData.append('file_type', templateData.file_type);
      formData.append('presentation_file', templateData.presentation_file);

      if (templateData.demo_video) {
        formData.append('demo_video', templateData.demo_video);
      }

      // Debug: Log FormData contents
      console.log('🔍 Debug - FormData contents:');
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}:`, {
            name: value.name,
            size: value.size,
            type: value.type
          });
        } else {
          console.log(`  ${key}:`, value);
        }
      }

      // Don't set Content-Type header manually for FormData - let axios handle it
      console.log('🚀 Making API request to /admin/templates');

      const response = await api.post<CreateTemplateResponse>('/admin/templates', formData, {
        // Add request interceptor to log the actual request
        transformRequest: [(data, headers) => {
          console.log('🔍 Request headers:', headers);
          console.log('🔍 Request data type:', typeof data);
          console.log('🔍 Request data instanceof FormData:', data instanceof FormData);
          if (data instanceof FormData) {
            console.log('🔍 FormData entries:');
            for (const [key, value] of data.entries()) {
              if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
              } else {
                console.log(`  ${key}: ${value}`);
              }
            }
          }
          return data;
        }]
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent créer des templates.');
      }
      if (error.response?.status === 422) {
        const validationErrors = error.response.data?.errors;
        if (validationErrors) {
          // Create a more detailed error message
          const errorMessages: string[] = [];
          Object.entries(validationErrors).forEach(([field, messages]) => {
            if (Array.isArray(messages)) {
              errorMessages.push(`${field}: ${messages.join(', ')}`);
            }
          });
          throw new Error(errorMessages.join(' | '));
        }
        throw new Error(error.response.data?.message || 'Erreur de validation');
      }
      if (error.response?.status === 413) {
        throw new Error('Le fichier est trop volumineux. Vérifiez les limites de votre serveur.');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la création du template');
    }
  }

  /**
   * Update an existing template (Admin only)
   */
  static async updateTemplate(id: number, templateData: UpdateTemplateRequest): Promise<CreateTemplateResponse> {
    try {
      // Create FormData for multipart/form-data
      const formData = new FormData();

      if (templateData.title) formData.append('title', templateData.title);
      if (templateData.description) formData.append('description', templateData.description);
      if (templateData.file_type) formData.append('file_type', templateData.file_type);
      if (templateData.presentation_file) formData.append('presentation_file', templateData.presentation_file);
      if (templateData.demo_video) formData.append('demo_video', templateData.demo_video);

      // Add method override for PUT request with FormData
      formData.append('_method', 'PUT');

      // Don't set Content-Type header manually for FormData - let axios handle it
      const response = await api.post<CreateTemplateResponse>(`/admin/templates/${id}`, formData);

      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent modifier des templates.');
      }
      if (error.response?.status === 404) {
        throw new Error('Template non trouvé');
      }
      if (error.response?.status === 422) {
        const validationErrors = error.response.data?.errors;
        if (validationErrors) {
          // Create a more detailed error message
          const errorMessages: string[] = [];
          Object.entries(validationErrors).forEach(([field, messages]) => {
            if (Array.isArray(messages)) {
              errorMessages.push(`${field}: ${messages.join(', ')}`);
            }
          });
          throw new Error(errorMessages.join(' | '));
        }
        throw new Error(error.response.data?.message || 'Erreur de validation');
      }
      if (error.response?.status === 413) {
        throw new Error('Le fichier est trop volumineux. Vérifiez les limites de votre serveur.');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du template');
    }
  }

  /**
   * Delete a template (Admin only)
   */
  static async deleteTemplate(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.delete<{ success: boolean; message: string }>(`/admin/templates/${id}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent supprimer des templates.');
      }
      if (error.response?.status === 404) {
        throw new Error('Template non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression du template');
    }
  }

  /**
   * Download a template file (Admin) - Legacy method for backward compatibility
   */
  static async downloadTemplate(id: number): Promise<Blob> {
    try {
      const response = await api.get(`/templates/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Vous n\'avez pas l\'autorisation de télécharger ce template.');
      }
      if (error.response?.status === 404) {
        throw new Error('Template non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors du téléchargement du template');
    }
  }

  /**
   * Download template presentation file (Admin & Collaborator only)
   */
  static async downloadPresentation(id: number): Promise<Blob> {
    try {
      const response = await api.get(`/templates/${id}/download/presentation`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Vous n\'avez pas l\'autorisation de télécharger cette présentation.');
      }
      if (error.response?.status === 404) {
        throw new Error('Présentation non trouvée');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors du téléchargement de la présentation');
    }
  }

  /**
   * Download template demo video (Admin & Collaborator only)
   */
  static async downloadVideo(id: number): Promise<Blob> {
    try {
      const response = await api.get(`/templates/${id}/download/video`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Vous n\'avez pas l\'autorisation de télécharger cette vidéo.');
      }
      if (error.response?.status === 404) {
        throw new Error('Vidéo de démonstration non trouvée');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors du téléchargement de la vidéo');
    }
  }

  /**
   * Stream template presentation file (Admin & Collaborator only)
   */
  static async streamPresentation(id: number): Promise<string> {
    try {
      const response = await api.get(`/templates/${id}/stream/presentation`);
      return response.data.stream_url;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Vous n\'avez pas l\'autorisation de visualiser cette présentation.');
      }
      if (error.response?.status === 404) {
        throw new Error('Présentation non trouvée');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors du streaming de la présentation');
    }
  }

  /**
   * Stream template demo video (Admin & Collaborator only)
   */
  static async streamVideo(id: number): Promise<string> {
    try {
      const response = await api.get(`/templates/${id}/stream/video`);
      return response.data.stream_url;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Vous n\'avez pas l\'autorisation de visualiser cette vidéo.');
      }
      if (error.response?.status === 404) {
        throw new Error('Vidéo de démonstration non trouvée');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors du streaming de la vidéo');
    }
  }

  /**
   * Toggle template active status (Admin only)
   */
  static async toggleTemplateStatus(id: number): Promise<Template> {
    try {
      const response = await api.patch<{ success: boolean; data: { template: Template } }>(`/templates/${id}/toggle-status`);
      return response.data.data.template;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent modifier le statut des templates.');
      }
      if (error.response?.status === 404) {
        throw new Error('Template non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification du statut');
    }
  }
}
