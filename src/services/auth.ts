import api from './api';

export interface LoginRequest {
  login: string; // Can be email or phone
  password: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  is_phone_verified: boolean;
  roles: string[];
  created_at?: string;
  updated_at?: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    token: string;
  };
}

export class AuthService {
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post<LoginResponse>('/auth/login', credentials);

      // Store token and user data in localStorage
      localStorage.setItem('auth_token', response.data.data.token);
      localStorage.setItem('user_data', JSON.stringify(response.data.data.user));

      return response.data;
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Échec de la connexion');
    }
  }

  static async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, clear local storage
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    }
  }

  static getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  static getUser(): User | null {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  }

  static isAuthenticated(): boolean {
    return !!this.getToken();
  }

  static hasRole(role: string): boolean {
    const user = this.getUser();
    return user?.roles.includes(role) || false;
  }

  static isAdmin(): boolean {
    return this.hasRole('Admin');
  }

  static isCollaborator(): boolean {
    return this.hasRole('Collaborator');
  }

  static isUser(): boolean {
    return this.hasRole('User');
  }
}

export default AuthService;
