import { useState, useEffect, useCallback } from 'react';
import { 
  TemplateService, 
  Template, 
  TemplateResponse, 
  CreateTemplateRequest, 
  UpdateTemplateRequest, 
  TemplateFilters 
} from '@/services/templates';

interface UseTemplatesReturn {
  templates: Template[];
  loading: boolean;
  error: string | null;
  pagination: TemplateResponse['data']['pagination'] | null;
  links: any | null;
  fetchTemplates: (filters?: TemplateFilters) => Promise<void>;
  createTemplate: (templateData: CreateTemplateRequest) => Promise<Template>;
  updateTemplate: (id: number, templateData: UpdateTemplateRequest) => Promise<Template>;
  deleteTemplate: (id: number) => Promise<void>;
  toggleTemplateStatus: (id: number) => Promise<void>;
  downloadTemplate: (id: number, filename?: string) => Promise<void>;
  downloadPresentation: (id: number, filename?: string) => Promise<void>;
  downloadVideo: (id: number, filename?: string) => Promise<void>;
  streamPresentation: (id: number) => Promise<string>;
  streamVideo: (id: number) => Promise<string>;
  clearError: () => void;
}

export const useTemplates = (initialFilters?: TemplateFilters): UseTemplatesReturn => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<TemplateResponse['data']['pagination'] | null>(null);
  const [links, setLinks] = useState<any | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchTemplates = useCallback(async (filters?: TemplateFilters) => {
    setLoading(true);
    setError(null);

    try {
      const response = await TemplateService.getTemplates(filters || initialFilters);
      setTemplates(response.data.templates);
      setPagination(response.data.pagination);
      setLinks(null); // Admin API doesn't provide links, only pagination
    } catch (err: any) {
      setError(err.message);
      setTemplates([]);
      setPagination(null);
      setLinks(null);
    } finally {
      setLoading(false);
    }
  }, []); // Remove initialFilters dependency to prevent infinite loop

  const createTemplate = useCallback(async (templateData: CreateTemplateRequest): Promise<Template> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await TemplateService.createTemplate(templateData);
      const newTemplate = response.data.template;
      
      // Add the new template to the beginning of the list
      setTemplates(prev => [newTemplate, ...prev]);
      
      return newTemplate;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTemplate = useCallback(async (id: number, templateData: UpdateTemplateRequest): Promise<Template> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await TemplateService.updateTemplate(id, templateData);
      const updatedTemplate = response.data.template;
      
      // Update the template in the list
      setTemplates(prev => 
        prev.map(template => 
          template.id === id ? updatedTemplate : template
        )
      );
      
      return updatedTemplate;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTemplate = useCallback(async (id: number): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await TemplateService.deleteTemplate(id);
      
      // Remove the template from the list
      setTemplates(prev => prev.filter(template => template.id !== id));
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const toggleTemplateStatus = useCallback(async (id: number): Promise<void> => {
    setError(null);
    
    try {
      const updatedTemplate = await TemplateService.toggleTemplateStatus(id);
      
      // Update the template status in the list
      setTemplates(prev => 
        prev.map(template => 
          template.id === id ? updatedTemplate : template
        )
      );
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, []);

  const downloadTemplate = useCallback(async (id: number, filename?: string): Promise<void> => {
    setError(null);

    try {
      const blob = await TemplateService.downloadTemplate(id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Set filename
      if (filename) {
        link.download = filename;
      } else {
        // Try to get filename from template data
        const template = templates.find(t => t.id === id);
        if (template) {
          const extension = template.presentation_file_url?.split('.').pop() || template.file_type || 'file';
          link.download = `${template.title}.${extension}`;
        } else {
          link.download = 'template-file';
        }
      }

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, [templates]);

  const downloadPresentation = useCallback(async (id: number, filename?: string): Promise<void> => {
    setError(null);

    try {
      const blob = await TemplateService.downloadPresentation(id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Set filename
      if (filename) {
        link.download = filename;
      } else {
        // Try to get filename from template data
        const template = templates.find(t => t.id === id);
        if (template) {
          const extension = template.file_type || 'pptx';
          link.download = `${template.title}_presentation.${extension}`;
        } else {
          link.download = 'presentation-file';
        }
      }

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, [templates]);

  const downloadVideo = useCallback(async (id: number, filename?: string): Promise<void> => {
    setError(null);

    try {
      const blob = await TemplateService.downloadVideo(id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Set filename
      if (filename) {
        link.download = filename;
      } else {
        // Try to get filename from template data
        const template = templates.find(t => t.id === id);
        if (template) {
          link.download = `${template.title}_demo.mp4`;
        } else {
          link.download = 'demo-video.mp4';
        }
      }

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, [templates]);

  const streamPresentation = useCallback(async (id: number): Promise<string> => {
    setError(null);

    try {
      const streamUrl = await TemplateService.streamPresentation(id);
      return streamUrl;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, []);

  const streamVideo = useCallback(async (id: number): Promise<string> => {
    setError(null);

    try {
      const streamUrl = await TemplateService.streamVideo(id);
      return streamUrl;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, []);

  // Initial fetch on mount with stable reference
  useEffect(() => {
    fetchTemplates(initialFilters);
  }, []); // Empty dependency array to run only once on mount

  return {
    templates,
    loading,
    error,
    pagination,
    links,
    fetchTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    toggleTemplateStatus,
    downloadTemplate,
    downloadPresentation,
    downloadVideo,
    streamPresentation,
    streamVideo,
    clearError,
  };
};

// Hook for managing a single template
export const useTemplate = (id: number) => {
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplate = useCallback(async () => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const templateData = await TemplateService.getTemplate(id);
      setTemplate(templateData);
    } catch (err: any) {
      setError(err.message);
      setTemplate(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchTemplate();
  }, [fetchTemplate]);

  return {
    template,
    loading,
    error,
    refetch: fetchTemplate,
    clearError: () => setError(null),
  };
};

// Hook for template file upload with progress
export const useTemplateUpload = () => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadTemplate = useCallback(async (templateData: CreateTemplateRequest): Promise<Template> => {
    setUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      // Simulate upload progress (since axios doesn't provide real progress for FormData)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await TemplateService.createTemplate(templateData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      return response.data.template;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, []);

  return {
    uploadTemplate,
    uploadProgress,
    uploading,
    error,
    clearError: () => setError(null),
  };
};
