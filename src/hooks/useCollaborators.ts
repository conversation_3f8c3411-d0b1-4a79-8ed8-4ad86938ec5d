import { useState, useEffect, useCallback } from 'react';
import { 
  CollaboratorService, 
  Collaborator, 
  CollaboratorFilters, 
  CreateCollaboratorRequest, 
  UpdateCollaboratorRequest 
} from '@/services/collaborators';

interface UseCollaboratorsReturn {
  collaborators: Collaborator[];
  loading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  } | null;
  fetchCollaborators: (filters?: CollaboratorFilters) => Promise<void>;
  createCollaborator: (data: CreateCollaboratorRequest) => Promise<Collaborator>;
  updateCollaborator: (id: number, data: UpdateCollaboratorRequest) => Promise<Collaborator>;
  deleteCollaborator: (id: number) => Promise<void>;
  toggleCollaboratorStatus: (id: number) => Promise<void>;
  clearError: () => void;
  refreshCollaborators: () => Promise<void>;
}

export const useCollaborators = (initialFilters?: CollaboratorFilters): UseCollaboratorsReturn => {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<{
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  } | null>(null);
  const [currentFilters, setCurrentFilters] = useState<CollaboratorFilters | undefined>(initialFilters);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchCollaborators = useCallback(async (filters?: CollaboratorFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = filters || currentFilters;
      setCurrentFilters(filtersToUse);
      
      const response = await CollaboratorService.getCollaborators(filtersToUse);
      setCollaborators(response.data.collaborators);
      setPagination(response.data.pagination || null);
    } catch (err: any) {
      setError(err.message);
      setCollaborators([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  }, [currentFilters]);

  const refreshCollaborators = useCallback(async () => {
    await fetchCollaborators(currentFilters);
  }, [fetchCollaborators, currentFilters]);

  const createCollaborator = useCallback(async (data: CreateCollaboratorRequest): Promise<Collaborator> => {
    try {
      setError(null);
      const newCollaborator = await CollaboratorService.createCollaborator(data);
      
      // Refresh the list to get updated data
      await refreshCollaborators();
      
      return newCollaborator;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, [refreshCollaborators]);

  const updateCollaborator = useCallback(async (id: number, data: UpdateCollaboratorRequest): Promise<Collaborator> => {
    try {
      setError(null);
      const updatedCollaborator = await CollaboratorService.updateCollaborator(id, data);
      
      // Update the collaborator in the local state
      setCollaborators(prev => 
        prev.map(collaborator => 
          collaborator.id === id ? updatedCollaborator : collaborator
        )
      );
      
      return updatedCollaborator;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, []);

  const deleteCollaborator = useCallback(async (id: number): Promise<void> => {
    try {
      setError(null);
      await CollaboratorService.deleteCollaborator(id);
      
      // Remove the collaborator from local state
      setCollaborators(prev => prev.filter(collaborator => collaborator.id !== id));
      
      // Update pagination if needed
      if (pagination) {
        setPagination(prev => prev ? { ...prev, total: prev.total - 1 } : null);
      }
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, [pagination]);

  const toggleCollaboratorStatus = useCallback(async (id: number): Promise<void> => {
    try {
      setError(null);
      const updatedCollaborator = await CollaboratorService.toggleCollaboratorStatus(id);
      
      // Update the collaborator in the local state
      setCollaborators(prev => 
        prev.map(collaborator => 
          collaborator.id === id ? updatedCollaborator : collaborator
        )
      );
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  }, []);

  // Initial fetch on mount
  useEffect(() => {
    fetchCollaborators(initialFilters);
  }, []); // Only run on mount

  return {
    collaborators,
    loading,
    error,
    pagination,
    fetchCollaborators,
    createCollaborator,
    updateCollaborator,
    deleteCollaborator,
    toggleCollaboratorStatus,
    clearError,
    refreshCollaborators,
  };
};
