"use client";

import { useEffect, useRef, useState } from 'react';

interface ScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  delay?: number;
  staggerDelay?: number;
  staggerChildren?: boolean;
}

interface ScrollAnimationReturn {
  ref: React.RefObject<HTMLDivElement>;
  isVisible: boolean;
  hasTriggered: boolean;
}

/**
 * Custom hook for scroll-triggered animations optimized for mobile devices
 * Provides smooth entrance animations when elements come into viewport
 */
export const useScrollAnimation = (options: ScrollAnimationOptions = {}): ScrollAnimationReturn => {
  const {
    threshold = 0.1,
    rootMargin = '0px 0px -50px 0px',
    triggerOnce = true,
    delay = 0,
  } = options;

  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setIsVisible(true);
      setHasTriggered(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // Apply delay if specified
          if (delay > 0) {
            setTimeout(() => {
              setIsVisible(true);
              if (triggerOnce) {
                setHasTriggered(true);
              }
            }, delay);
          } else {
            setIsVisible(true);
            if (triggerOnce) {
              setHasTriggered(true);
            }
          }
        } else if (!triggerOnce && !hasTriggered) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce, delay, hasTriggered]);

  return { ref, isVisible, hasTriggered };
};

/**
 * Hook for staggered animations of multiple children elements
 */
export const useStaggeredAnimation = (
  itemCount: number,
  options: ScrollAnimationOptions = {}
): {
  containerRef: React.RefObject<HTMLDivElement>;
  getItemProps: (index: number) => {
    style: React.CSSProperties;
    className: string;
  };
} => {
  const { staggerDelay = 100, threshold = 0.1, rootMargin = '0px 0px -50px 0px' } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = containerRef.current;
    if (!element) return;

    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin]);

  const getItemProps = (index: number) => ({
    style: {
      animationDelay: isVisible ? `${index * staggerDelay}ms` : '0ms',
      animationFillMode: 'both' as const,
    },
    className: isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-8',
  });

  return { containerRef, getItemProps };
};

/**
 * Hook for scroll progress tracking (useful for progress bars, etc.)
 */
export const useScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setScrollProgress(Math.min(100, Math.max(0, progress)));
    };

    window.addEventListener('scroll', updateScrollProgress, { passive: true });
    updateScrollProgress(); // Initial calculation

    return () => window.removeEventListener('scroll', updateScrollProgress);
  }, []);

  return scrollProgress;
};

/**
 * Hook for parallax scrolling effects (optimized for mobile performance)
 */
export const useParallax = (speed: number = 0.5) => {
  const ref = useRef<HTMLDivElement>(null);
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Disable parallax on mobile devices for better performance
    const isMobile = window.innerWidth < 768;
    if (isMobile) return;

    const updateOffset = () => {
      const scrollTop = window.pageYOffset;
      const elementTop = element.offsetTop;
      const elementHeight = element.offsetHeight;
      const windowHeight = window.innerHeight;

      // Only calculate parallax when element is in viewport
      if (scrollTop + windowHeight > elementTop && scrollTop < elementTop + elementHeight) {
        const yPos = -(scrollTop - elementTop) * speed;
        setOffset(yPos);
      }
    };

    window.addEventListener('scroll', updateOffset, { passive: true });
    updateOffset(); // Initial calculation

    return () => window.removeEventListener('scroll', updateOffset);
  }, [speed]);

  return { ref, offset };
};
