"use client";

import SectionTitle from "../Common/SectionTitle";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useI18n } from "@/i18n/context";
import { useRouter } from "next/navigation";

const ServicesOverview = () => {
  const { t, isRTL } = useI18n();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  const router = useRouter();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const checkIcon = (
    <svg width="16" height="13" viewBox="0 0 16 13" className="fill-current">
      <path d="M5.8535 12.6631C5.65824 12.8584 5.34166 12.8584 5.1464 12.6631L0.678505 8.1952C0.483242 7.99994 0.483242 7.68336 0.678505 7.4881L2.32921 5.83739C2.52467 5.64193 2.84166 5.64216 3.03684 5.83791L5.14622 7.95354C5.34147 8.14936 5.65859 8.14952 5.85403 7.95388L13.3797 0.420561C13.575 0.22513 13.8917 0.225051 14.087 0.420383L15.7381 2.07143C15.9333 2.26669 15.9333 2.58327 15.7381 2.77854L5.8535 12.6631Z" />
    </svg>
  );

  // Predefined positions for floating elements to avoid hydration mismatch
  const floatingPositions = [
    { left: 15, top: 20 },
    { left: 85, top: 15 },
    { left: 25, top: 80 },
    { left: 75, top: 70 },
    { left: 45, top: 30 },
    { left: 65, top: 85 }
  ];

  // Floating animation keyframes with fixed positions
  const floatingElements = Array.from({ length: 6 }, (_, i) => (
    <div
      key={i}
      className={`absolute opacity-10 animate-pulse ${
        i % 2 === 0 ? 'animate-bounce' : 'animate-ping'
      }`}
      style={{
        left: `${floatingPositions[i].left}%`,
        top: `${floatingPositions[i].top}%`,
        animationDelay: `${i * 0.5}s`,
        animationDuration: `${3 + i}s`,
      }}
    >
      <div className={`w-2 h-2 rounded-full ${i % 3 === 0 ? 'bg-primary' : i % 3 === 1 ? 'bg-secondary' : 'bg-primary/70'}`} />
    </div>
  ));

  // Word logo SVG with enhanced animations
  const WordLogoSVG = () => (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      className="fill-current text-white transition-all duration-500 group-hover:rotate-3 group-hover:scale-110"
    >
      <rect width="48" height="48" rx="4" fill="#2B579A"/>
      <path d="M12 12h24v24H12V12z" fill="#2B579A"/>
      <path d="M16 16l2 8 2-8h2l2 8 2-8h2l-3 16h-2l-2-8-2 8h-2L16 16z" fill="white"/>
    </svg>
  );

  const ServiceCard = ({
    logoSrc,
    title,
    features,
    bgColor,
    hoverColor,
    logoAlt,
    useCustomLogo = false,
    index = 0
  }: {
    logoSrc?: string;
    title: string;
    features: string[];
    bgColor: string;
    hoverColor: string;
    logoAlt: string;
    useCustomLogo?: boolean;
    index?: number;
  }) => (
    <div
      className={`group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-sm p-8 shadow-xl transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10 dark:bg-gray-dark/80 border border-gray-100 dark:border-gray-700 hover:border-primary/20 transform hover:scale-[1.02] hover:-translate-y-2 ${
        isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0 translate-y-8'
      }`}
      style={{
        animationDelay: `${index * 200}ms`,
        animationFillMode: 'forwards'
      }}
    >
      {/* Animated background gradient overlay */}
      <div className={`absolute inset-0 opacity-0 transition-all duration-500 group-hover:opacity-10 bg-gradient-to-br ${bgColor.replace('bg-', 'from-')} to-transparent`}></div>

      {/* Floating particles effect with fixed positions */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[
          { left: 20, top: 20 },
          { left: 50, top: 40 },
          { left: 80, top: 60 }
        ].map((pos, i) => (
          <div
            key={i}
            className={`absolute w-1 h-1 rounded-full opacity-0 group-hover:opacity-30 transition-all duration-1000 ${bgColor.replace('bg-', 'bg-')}`}
            style={{
              left: `${pos.left}%`,
              top: `${pos.top}%`,
              animationDelay: `${i * 300}ms`,
            }}
          />
        ))}
      </div>

      {/* Premium logo container with enhanced animations */}
      <div className={`relative mb-8 inline-flex h-24 w-24 items-center justify-center rounded-2xl ${bgColor} shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 group-hover:shadow-3xl overflow-hidden`}>
        {/* Logo glow effect */}
        <div className={`absolute inset-0 rounded-2xl ${bgColor} opacity-0 group-hover:opacity-20 blur-xl transition-all duration-500 scale-150`}></div>

        {/* Logo content */}
        <div className="relative z-10">
          {useCustomLogo ? (
            <WordLogoSVG />
          ) : logoSrc ? (
            <Image
              src={logoSrc}
              alt={logoAlt}
              width={56}
              height={56}
              className="object-contain transition-all duration-500 group-hover:scale-110 group-hover:rotate-2"
            />
          ) : null}
        </div>

        {/* Rotating border effect */}
        <div className="absolute inset-0 rounded-2xl border-2 border-white/20 group-hover:border-white/40 transition-all duration-500"></div>
      </div>

      {/* Enhanced title with gradient text effect */}
      <h3 className="relative mb-8 text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent transition-all duration-500 group-hover:from-primary group-hover:to-primary/70 leading-tight">
        {title}
      </h3>

      {/* Premium features list with staggered animations */}
      <ul className="space-y-5">
        {features.map((feature, featureIndex) => (
          <li
            key={featureIndex}
            className="flex items-start group/item transition-all duration-300 hover:translate-x-2"
            style={{
              animationDelay: `${(index * 200) + (featureIndex * 100)}ms`
            }}
          >
            <span className="bg-gradient-to-r from-primary to-primary/70 text-white mr-4 mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full shadow-lg transition-all duration-300 group-hover/item:scale-110 group-hover/item:shadow-primary/30">
              {checkIcon}
            </span>
            <span className="text-gray-700 dark:text-gray-300 text-base font-medium leading-relaxed transition-all duration-300 group-hover/item:text-gray-900 dark:group-hover/item:text-white">
              {feature}
            </span>
          </li>
        ))}
      </ul>

      {/* Enhanced decorative elements */}
      <div className="absolute -right-6 -top-6 h-32 w-32 rounded-full bg-gradient-to-br from-primary/10 via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 blur-xl"></div>
      <div className="absolute -bottom-4 -left-4 h-20 w-20 rounded-full bg-gradient-to-tr from-primary/5 to-transparent transition-all duration-500 group-hover:scale-150"></div>

      {/* Subtle border animation */}
      <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-1000 animate-pulse"></div>
    </div>
  );

  return (
    <section
      ref={sectionRef}
      id="services"
      className="relative py-20 md:py-24 lg:py-32 overflow-hidden"
    >
      {/* Premium background with animated gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50/80 via-white to-blue-50/30 dark:from-gray-900/50 dark:via-gray-800/30 dark:to-blue-900/10"></div>

      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {floatingElements}

        {/* Large decorative circles */}
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 animate-pulse" style={{ animationDuration: '4s' }}></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/20 to-secondary/10 animate-pulse" style={{ animationDuration: '6s', animationDelay: '2s' }}></div>

        {/* Animated mesh gradient */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-l from-secondary/10 to-transparent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>
        </div>
      </div>

      <div className="container relative z-10">
        {/* Enhanced Section Header */}
        <div className={`mb-20 text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="relative">
            <SectionTitle
              title={t('services.title')}
              paragraph={t('services.subtitle')}
              center
              width="900px"
              mb="0px"
            />

            {/* Decorative elements around title */}
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-primary to-transparent"></div>
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-secondary to-transparent"></div>
          </div>
        </div>

        {/* Modern Services Grid - Card Based Design */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-20">
          {/* PowerPoint Service - Large Card */}
          <div className="lg:col-span-2 lg:row-span-2">
            <div
              onClick={() => router.push('/services/powerpoint')}
              className={`group relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#D24726] to-[#B73E1C] p-8 h-full min-h-[400px] text-white transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] cursor-pointer ${
                isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '0ms' }}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-10 right-10 w-32 h-32 rounded-full bg-white/20"></div>
                <div className="absolute bottom-20 left-10 w-24 h-24 rounded-full bg-white/10"></div>
                <div className="absolute top-1/2 right-1/4 w-16 h-16 rounded-full bg-white/15"></div>
              </div>

              {/* Service Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-white/20 rounded-full text-sm font-medium mb-6">
                <span>Services</span>
              </div>

              {/* Logo */}
              <div className="absolute top-8 right-8 w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <Image src="/images/powerpointe.png" alt="PowerPoint" width={32} height={32} className="opacity-90" />
              </div>

              {/* Content */}
              <div className="relative z-10 h-full flex flex-col justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-2xl">🔴</span>
                    <h3 className="text-3xl lg:text-4xl font-bold leading-tight">
                      {t('about.powerpoint.title')}
                    </h3>
                  </div>

                  <div className="mb-6">
                    <p className="text-white/90 text-lg mb-4 leading-relaxed max-w-md">
                      Création de présentations PowerPoint impactantes, professionnelles et adaptées à vos besoins (présentation de projet, soutenance, pitch commercial, etc.).
                    </p>

                    {/* Features */}
                    <div className="space-y-2 mb-6">
                      <div className="flex items-center gap-2 text-white/90">
                        <span>✨</span>
                        <span className="text-sm">Design moderne & sur-mesure</span>
                      </div>
                      <div className="flex items-center gap-2 text-white/90">
                        <span>🕐</span>
                        <span className="text-sm">Livraison rapide</span>
                      </div>
                      <div className="flex items-center gap-2 text-white/90">
                        <span>✅</span>
                        <span className="text-sm">Correction & révision incluses</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <svg className="w-5 h-5 fill-current text-yellow" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <span className="font-semibold">4.9</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="font-semibold">95%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Word Service - Medium Card */}
          <div className="lg:row-span-1">
            <div
              onClick={() => router.push('/services/document')}
              className={`group relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#2B579A] to-[#1F4788] p-6 h-full min-h-[190px] text-white transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] cursor-pointer ${
                isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '200ms' }}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 right-4 w-20 h-20 rounded-full bg-white/20"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 rounded-full bg-white/15"></div>
              </div>

              {/* Service Badge */}
              <div className="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-xs font-medium mb-4">
                <span>Services</span>
              </div>

              {/* Logo */}
              <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <Image src="/images/Word.png" alt="Word" width={24} height={24} className="opacity-90" />
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-lg">🔵</span>
                  <h3 className="text-xl font-bold leading-tight">
                    {t('about.word.title')}
                  </h3>
                </div>

                <p className="text-white/90 text-sm mb-4 leading-relaxed">
                  Mise en page et rédaction professionnelle de vos documents Word : rapports, lettres de motivation, documents administratifs...
                </p>

                {/* Stats */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <span className="text-xs">⭐</span>
                    <span className="font-semibold text-sm">Note : 4.8 / 5</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs">👍</span>
                    <span className="font-semibold text-sm">Satisfaction : 92%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CV Service - Medium Card */}
          <div className="lg:row-span-1">
            <div
              onClick={() => router.push('/services/cv')}
              className={`group relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1CBCCF] to-[#2E86C1] p-6 h-full min-h-[190px] text-white transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] cursor-pointer ${
                isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0 translate-y-8'
              }`}
              style={{ animationDelay: '400ms' }}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 right-4 w-20 h-20 rounded-full bg-white/20"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 rounded-full bg-white/15"></div>
              </div>

              {/* Service Badge */}
              <div className="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-xs font-medium mb-4">
                <span>Services</span>
              </div>

              {/* Logo */}
              <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 fill-current text-white" viewBox="0 0 24 24">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-lg">🔷</span>
                  <h3 className="text-xl font-bold leading-tight">
                    {t('about.cv.title')}
                  </h3>
                </div>

                <p className="text-white/90 text-sm mb-4 leading-relaxed">
                  Création ou refonte de CV modernes et percutants, adaptés à votre domaine et vos ambitions professionnelles.
                </p>

                {/* Stats */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <span className="text-xs">⭐</span>
                    <span className="font-semibold text-sm">Note : 4.7 / 5</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs">👍</span>
                    <span className="font-semibold text-sm">Satisfaction : 88%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Call to Action */}
        <div className={`text-center transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="relative mx-auto max-w-4xl rounded-3xl bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-12 shadow-2xl backdrop-blur-sm border border-primary/10 overflow-hidden group hover:shadow-3xl transition-all duration-500">
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

            {/* Content */}
            <div className={`relative z-10 ${isRTL ? 'text-right' : 'text-left'} md:text-center`}>
              <h3 className="mb-6 text-3xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent dark:from-white dark:to-primary-light">
                {t('cta.title')}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-8 text-lg leading-relaxed max-w-2xl mx-auto">
                {t('cta.subtitle')}
              </p>

              {/* Premium CTA Button */}
              <button className="group/btn relative inline-flex items-center justify-center px-10 py-4 text-lg font-semibold text-white bg-gradient-to-r from-primary to-primary/80 rounded-full shadow-lg hover:shadow-xl hover:shadow-primary/25 transition-all duration-300 hover:scale-105 hover:-translate-y-1 overflow-hidden">
                {/* Button background animation */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary opacity-0 group-hover/btn:opacity-100 transition-all duration-300"></div>

                {/* Button content */}
                <span className={`relative z-10 ${isRTL ? 'ml-2' : 'mr-2'}`}>{t('cta.button')}</span>
                <svg className={`relative z-10 w-5 h-5 transition-transform duration-300 ${isRTL ? 'group-hover/btn:-translate-x-1' : 'group-hover/btn:translate-x-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isRTL ? "M11 17l-5-5m0 0l5-5m-5 5h12" : "M13 7l5 5m0 0l-5 5m5-5H6"} />
                </svg>

                {/* Button shine effect */}
                <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover/btn:opacity-100 group-hover/btn:animate-shine"></div>
              </button>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 rounded-full bg-gradient-to-br from-primary/20 to-transparent blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-24 h-24 rounded-full bg-gradient-to-tr from-secondary/20 to-transparent blur-xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesOverview;
