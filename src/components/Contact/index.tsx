"use client";

import { useI18n } from "@/i18n/context";

const Contact = () => {
  const { t, isRTL } = useI18n();

  return (
    <section
      id="contact"
      className="overflow-hidden mobile-section-padding py-12 sm:py-16 md:py-20 lg:py-28"
      style={{
        backgroundImage: 'url(/public/cntactbg.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background overlay for better form readability */}
      <div className="absolute inset-0 bg-white/90 dark:bg-gray-dark/90"></div>

      <div className="container relative z-10">
        <div className="-mx-4 flex flex-wrap justify-center">
          <div className="w-full px-4 lg:w-8/12 xl:w-6/12">
            <div
              className={`mb-8 sm:mb-12 rounded-xs bg-white px-6 py-8 sm:px-8 sm:py-11 shadow-three dark:bg-gray-dark lg:mb-5 lg:px-8 xl:p-[55px] ${isRTL ? 'text-right' : 'text-left'}`}
              data-wow-delay=".15s"
            >
              <h2 className="mb-2 sm:mb-3 text-xl sm:text-2xl font-bold text-black dark:text-white lg:text-2xl xl:text-3xl">
                {t('contact.title')}
              </h2>
              <p className="mb-8 sm:mb-12 text-sm sm:text-base font-medium text-body-color">
                {t('contact.description')}
              </p>
              <form>
                <div className="-mx-4 flex flex-wrap">
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-6 sm:mb-8">
                      <label
                        htmlFor="name"
                        className="mb-2 sm:mb-3 block text-xs sm:text-sm font-medium text-dark dark:text-white"
                      >
                        {t('contact.form.name.label')}
                      </label>
                      <input
                        type="text"
                        placeholder={t('contact.form.name.placeholder')}
                        className={`border-stroke w-full rounded-xs border bg-[#f8f8f8] px-4 py-3 sm:px-6 sm:py-3 text-sm sm:text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none mobile-touch-button ${isRTL ? 'text-right' : 'text-left'}`}
                      />
                    </div>
                  </div>
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-6 sm:mb-8">
                      <label
                        htmlFor="email"
                        className="mb-2 sm:mb-3 block text-xs sm:text-sm font-medium text-dark dark:text-white"
                      >
                        {t('contact.form.email.label')}
                      </label>
                      <input
                        type="email"
                        placeholder={t('contact.form.email.placeholder')}
                        className={`border-stroke w-full rounded-xs border bg-[#f8f8f8] px-4 py-3 sm:px-6 sm:py-3 text-sm sm:text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none mobile-touch-button ${isRTL ? 'text-right' : 'text-left'}`}
                      />
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <div className="mb-6 sm:mb-8">
                      <label
                        htmlFor="message"
                        className="mb-2 sm:mb-3 block text-xs sm:text-sm font-medium text-dark dark:text-white"
                      >
                        {t('contact.form.message.label')}
                      </label>
                      <textarea
                        name="message"
                        rows={4}
                        placeholder={t('contact.form.message.placeholder')}
                        className={`border-stroke w-full resize-none rounded-xs border bg-[#f8f8f8] px-4 py-3 sm:px-6 sm:py-3 text-sm sm:text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none mobile-touch-button ${isRTL ? 'text-right' : 'text-left'}`}
                      ></textarea>
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <button className="rounded-xs bg-primary px-6 py-3 sm:px-9 sm:py-4 text-sm sm:text-base font-medium text-white shadow-submit duration-300 hover:bg-primary/90 dark:shadow-submit-dark mobile-touch-button w-full sm:w-auto">
                      {t('contact.form.submit')}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
