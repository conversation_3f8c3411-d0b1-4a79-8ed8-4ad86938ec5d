"use client";

import { usePathname } from "next/navigation";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ScrollToTop from "@/components/ScrollToTop";
import WhatsAppButton from "@/components/WhatsAppButton";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

const ConditionalLayout = ({ children }: ConditionalLayoutProps) => {
  const pathname = usePathname();
  
  // Check if current route is admin, collaborator, or login page
  const isAdminRoute = pathname?.startsWith('/admin');
  const isCollaboratorRoute = pathname?.startsWith('/collaborator');
  const isLoginRoute = pathname === '/login';

  // Don't show header, footer, scroll to top, and WhatsApp button on admin and collaborator routes
  if (isAdminRoute || isCollaboratorRoute) {
    return <>{children}</>;
  }
  
  // For login page, show minimal layout (no header/footer but keep scroll to top)
  if (isLoginRoute) {
    return (
      <>
        {children}
        <ScrollToTop />
      </>
    );
  }
  
  // Default layout for all other pages
  return (
    <>
      <Header />
      {children}
      <Footer />
      <ScrollToTop />
      <WhatsAppButton />
    </>
  );
};

export default ConditionalLayout;
