"use client";

import Image from "next/image";
import { AuthService } from "@/services/auth";

interface AdminMobileHeaderProps {
  setSidebarOpen: (open: boolean) => void;
}

const AdminMobileHeader = ({ setSidebarOpen }: AdminMobileHeaderProps) => {
  const user = AuthService.getUser();

  return (
    <div className="lg:hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl shadow-lg border-b border-gray-200/50 dark:border-gray-700/50 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-105"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <Image
            src="/images/logo.png"
            alt="Sademy Logo"
            width={32}
            height={32}
            className="h-8 w-8"
          />
          <h1 className="text-lg font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Sademy Admin
          </h1>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-secondary text-white rounded-xl flex items-center justify-center text-sm font-bold shadow-lg">
            {user?.name.charAt(0).toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminMobileHeader;
