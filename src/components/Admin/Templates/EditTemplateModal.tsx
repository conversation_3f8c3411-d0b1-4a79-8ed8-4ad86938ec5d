"use client";

import { useState, useEffect } from 'react';
import { useTemplates } from '@/hooks/useTemplates';
import { Template, UpdateTemplateRequest } from '@/services/templates';

interface EditTemplateModalProps {
  template: Template;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (template: Template) => void;
}

const EditTemplateModal = ({ template, isOpen, onClose, onSuccess }: EditTemplateModalProps) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'ppt',
    is_active: true,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<File | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { updateTemplate } = useTemplates();

  // Initialize form data when template changes
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description,
        type: template.type,
        is_active: template.is_active,
      });
    }
  }, [template]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du template est requis';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    }

    // Validate file type if a new file is selected
    if (selectedFile) {
      const allowedTypes = {
        ppt: ['.pptx', '.ppt'],
        doc: ['.docx', '.doc'],
        pdf: ['.pdf']
      };
      
      const extension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
      if (!allowedTypes[formData.type as keyof typeof allowedTypes]?.includes(extension)) {
        newErrors.file = `Type de fichier invalide pour ${formData.type.toUpperCase()}. Extensions autorisées: ${allowedTypes[formData.type as keyof typeof allowedTypes]?.join(', ')}`;
      }

      // Validate file size (200MB max)
      const maxSize = 200 * 1024 * 1024;
      if (selectedFile.size > maxSize) {
        newErrors.file = 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.';
      }
    }

    // Validate video file if provided
    if (selectedVideo) {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv'];
      const videoExtension = selectedVideo.name.toLowerCase().substring(selectedVideo.name.lastIndexOf('.'));
      if (!videoExtensions.includes(videoExtension)) {
        newErrors.video = `Type de vidéo invalide. Extensions autorisées: ${videoExtensions.join(', ')}`;
      }

      // Validate video size (100MB max)
      const maxVideoSize = 100 * 1024 * 1024;
      if (selectedVideo.size > maxVideoSize) {
        newErrors.video = 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    const updateData: UpdateTemplateRequest = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      type: formData.type,
      is_active: formData.is_active,
    };

    // Only include files if they were changed
    if (selectedFile) {
      updateData.file = selectedFile;
    }
    if (selectedVideo) {
      updateData.demo_video = selectedVideo;
    }

    try {
      const updatedTemplate = await updateTemplate(template.id, updateData);
      
      // Reset form
      setSelectedFile(null);
      setSelectedVideo(null);
      setErrors({});
      
      // Call success callback
      onSuccess?.(updatedTemplate);
      onClose();
    } catch (error: any) {
      setErrors({ submit: error.message || 'Erreur lors de la mise à jour du template' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // Clear file error when a file is selected
      if (errors.file) {
        setErrors(prev => ({ ...prev, file: '' }));
      }
    }
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedVideo(file);
      // Clear video error when a file is selected
      if (errors.video) {
        setErrors(prev => ({ ...prev, video: '' }));
      }
    }
  };

  const handleClose = () => {
    if (isSubmitting) {
      return; // Prevent closing during submission
    }
    
    // Reset form
    setSelectedFile(null);
    setSelectedVideo(null);
    setErrors({});
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Modifier le template: {template.name}
          </h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {errors.submit && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-800 dark:text-red-200">{errors.submit}</p>
              </div>
            </div>
          )}

          {/* Template Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nom du template *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => {
                setFormData({ ...formData, name: e.target.value });
                if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                errors.name ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Ex: Présentation Business Professionnelle"
              disabled={isSubmitting}
            />
            {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => {
                setFormData({ ...formData, description: e.target.value });
                if (errors.description) setErrors(prev => ({ ...prev, description: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
              }`}
              rows={3}
              placeholder="Description détaillée du template..."
              disabled={isSubmitting}
            />
            {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
          </div>

          {/* Template Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type de template *
            </label>
            <select
              value={formData.type}
              onChange={(e) => {
                setFormData({ ...formData, type: e.target.value });
                // Clear file when type changes
                setSelectedFile(null);
                if (errors.file) setErrors(prev => ({ ...prev, file: '' }));
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
              disabled={isSubmitting}
            >
              <option value="ppt">PowerPoint (.pptx, .ppt)</option>
              <option value="doc">Word (.docx, .doc)</option>
              <option value="pdf">PDF (.pdf)</option>
            </select>
          </div>

          {/* Current File Info */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Fichier actuel
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {template.file_url.split('/').pop()}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Fichier actuel - {template.type.toUpperCase()}
                  </p>
                </div>
                <a
                  href={template.file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary/80 text-sm"
                >
                  Télécharger
                </a>
              </div>
            </div>
          </div>

          {/* New File Upload (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Remplacer le fichier (optionnel)
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              accept={
                formData.type === 'ppt' ? '.pptx,.ppt' :
                formData.type === 'doc' ? '.docx,.doc' :
                '.pdf'
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
              disabled={isSubmitting}
            />
            {selectedFile && (
              <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(selectedFile.size)}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedFile(null)}
                    disabled={isSubmitting}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {errors.file && <p className="text-red-600 text-sm mt-1">{errors.file}</p>}
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Laissez vide pour conserver le fichier actuel
            </p>
          </div>

          {/* Video Upload (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Vidéo de démonstration (optionnel)
            </label>
            {template.demo_video_url && (
              <div className="mb-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Vidéo actuelle
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {template.demo_video_url.split('/').pop()}
                    </p>
                  </div>
                  <a
                    href={template.demo_video_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary/80 text-sm"
                  >
                    Voir
                  </a>
                </div>
              </div>
            )}
            <input
              type="file"
              onChange={handleVideoChange}
              accept=".mp4,.avi,.mov,.wmv"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
              disabled={isSubmitting}
            />
            {selectedVideo && (
              <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{selectedVideo.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(selectedVideo.size)}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedVideo(null)}
                    disabled={isSubmitting}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {errors.video && <p className="text-red-600 text-sm mt-1">{errors.video}</p>}
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Formats supportés: MP4, AVI, MOV, WMV (max 100MB)
            </p>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active_edit"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              disabled={isSubmitting}
            />
            <label htmlFor="is_active_edit" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Template actif (visible pour les utilisateurs)
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Mise à jour...</span>
                </>
              ) : (
                <span>Mettre à jour</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditTemplateModal;
