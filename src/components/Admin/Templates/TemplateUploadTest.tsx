"use client";

import { useState } from 'react';
import { useTemplateUpload } from '@/hooks/useTemplates';
import { CreateTemplateRequest } from '@/services/templates';

/**
 * Test component to verify template upload fixes
 * This component can be used to test the upload functionality
 */
const TemplateUploadTest = () => {
  const [formData, setFormData] = useState({
    name: 'Test Template',
    description: 'Test template for debugging upload issues',
    type: 'ppt',
    is_active: true,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const {
    uploadTemplate,
    uploadProgress,
    uploading,
    error: uploadError,
    clearError,
  } = useTemplateUpload();

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testApiConnection = async () => {
    addTestResult('🔗 Testing API connection...');
    try {
      // Test basic API connectivity
      const token = localStorage.getItem('auth_token');
      addTestResult(`🔑 Auth token: ${token ? 'Present' : 'Missing'}`);

      // Try to fetch templates to test API
      const response = await fetch('http://localhost:8000/api/v1/templates', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      addTestResult(`📡 API Response: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const data = await response.json();
        addTestResult(`✅ API connection successful. Templates count: ${data.data?.length || 0}`);
      } else {
        const errorData = await response.text();
        addTestResult(`❌ API error: ${errorData}`);
      }
    } catch (error: any) {
      addTestResult(`❌ Connection failed: ${error.message}`);
    }
  };

  const handleTestUpload = async () => {
    if (!selectedFile) {
      addTestResult('❌ No file selected');
      return;
    }

    addTestResult('🚀 Starting upload test...');
    addTestResult(`📁 File: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
    addTestResult(`📋 Form data: ${JSON.stringify(formData)}`);

    const templateData: CreateTemplateRequest = {
      name: formData.name,
      description: formData.description,
      type: formData.type,
      file: selectedFile,
      is_active: formData.is_active,
    };

    try {
      const result = await uploadTemplate(templateData);
      addTestResult('✅ Upload successful!');
      addTestResult(`📄 Template created: ${JSON.stringify(result, null, 2)}`);
    } catch (error: any) {
      addTestResult(`❌ Upload failed: ${error.message}`);
      console.error('Upload error details:', error);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    clearError();
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
        Template Upload Test
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Form */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">Test Form</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="ppt">PowerPoint</option>
              <option value="doc">Word</option>
              <option value="pdf">PDF</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active_test"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active_test" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Active Template (is_active: {formData.is_active ? 'true' : 'false'})
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              File
            </label>
            <input
              type="file"
              onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
              accept={
                formData.type === 'ppt' ? '.pptx,.ppt' :
                formData.type === 'doc' ? '.docx,.doc' :
                '.pdf'
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Upload Progress</span>
                <span className="text-blue-600 font-medium">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className="flex space-x-3">
            <button
              onClick={testApiConnection}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Test API
            </button>
            <button
              onClick={handleTestUpload}
              disabled={uploading || !selectedFile}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? 'Uploading...' : 'Test Upload'}
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">Test Results</h3>
          
          {uploadError && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-200 font-medium">Upload Error:</p>
              <p className="text-red-700 dark:text-red-300 text-sm mt-1">{uploadError}</p>
            </div>
          )}

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">No test results yet...</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700 dark:text-gray-300">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Fixes Applied:</h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>✅ Fixed is_active field: now sends "1"/"0" instead of "true"/"false"</li>
          <li>✅ Removed manual Content-Type header for FormData</li>
          <li>✅ Added better file validation before upload</li>
          <li>✅ Improved error handling for validation errors</li>
          <li>✅ Added file size validation (50MB for templates, 100MB for videos)</li>
        </ul>
      </div>
    </div>
  );
};

export default TemplateUploadTest;
