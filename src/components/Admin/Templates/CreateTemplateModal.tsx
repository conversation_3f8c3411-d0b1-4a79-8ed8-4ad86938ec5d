"use client";

import { useState } from 'react';
import { useTemplateUpload } from '@/hooks/useTemplates';
import { CreateTemplateRequest } from '@/services/templates';

interface CreateTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (template: any) => void;
}

const CreateTemplateModal = ({ isOpen, onClose, onSuccess }: CreateTemplateModalProps) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    file_type: 'pptx',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<File | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const {
    uploadTemplate,
    uploadProgress,
    uploading,
    error: uploadError,
    clearError,
  } = useTemplateUpload();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    console.log('🔍 VALIDATION - Starting form validation');
    console.log('🔍 VALIDATION - selectedFile:', selectedFile ? {
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type
    } : 'NULL');

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre du template est requis';
      console.log('❌ VALIDATION - Title is required');
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
      console.log('❌ VALIDATION - Description is required');
    }

    if (!selectedFile) {
      newErrors.file = 'Veuillez sélectionner un fichier';
      console.log('❌ VALIDATION - No file selected');
    } else {
      console.log('✅ VALIDATION - File is present, validating...');
      // Validate file type
      const allowedTypes = {
        pptx: ['.pptx', '.ppt'],
        docx: ['.docx', '.doc'],
        pdf: ['.pdf']
      };

      const extension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
      console.log('🔍 VALIDATION - File extension:', extension, 'Expected type:', formData.file_type);

      if (!allowedTypes[formData.file_type as keyof typeof allowedTypes]?.includes(extension)) {
        newErrors.file = `Type de fichier invalide pour ${formData.file_type.toUpperCase()}. Extensions autorisées: ${allowedTypes[formData.file_type as keyof typeof allowedTypes]?.join(', ')}`;
        console.log('❌ VALIDATION - Invalid file type');
      }

      // Validate file size (200MB max)
      const maxSize = 200 * 1024 * 1024;
      if (selectedFile.size > maxSize) {
        newErrors.file = 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.';
        console.log('❌ VALIDATION - File too large');
      }

      if (!newErrors.file) {
        console.log('✅ VALIDATION - File validation passed');
      }
    }

    // Validate video file if provided
    if (selectedVideo) {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv'];
      const videoExtension = selectedVideo.name.toLowerCase().substring(selectedVideo.name.lastIndexOf('.'));
      if (!videoExtensions.includes(videoExtension)) {
        newErrors.video = `Type de vidéo invalide. Extensions autorisées: ${videoExtensions.join(', ')}`;
      }

      // Validate video file size (100MB max)
      const maxVideoSize = 100 * 1024 * 1024;
      if (selectedVideo.size > maxVideoSize) {
        newErrors.video = 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.';
      }
    }

    console.log('🔍 VALIDATION - Errors found:', newErrors);
    console.log('🔍 VALIDATION - Validation result:', Object.keys(newErrors).length === 0 ? 'PASSED' : 'FAILED');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Debug: Log form state before validation
    console.log('🔍 Debug - Form submission:', {
      formData,
      selectedFile: selectedFile ? {
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type,
        lastModified: selectedFile.lastModified
      } : 'NO FILE',
      selectedVideo: selectedVideo ? {
        name: selectedVideo.name,
        size: selectedVideo.size,
        type: selectedVideo.type
      } : 'NO VIDEO'
    });

    if (!validateForm()) {
      console.log('❌ Form validation failed');
      return;
    }

    if (!selectedFile) {
      console.log('❌ CRITICAL - No file selected at submission time');
      console.log('❌ CRITICAL - This should not happen if file was properly selected');
      alert('Veuillez sélectionner un fichier');
      return;
    }

    console.log('✅ SUBMISSION - File is present:', {
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type,
      lastModified: selectedFile.lastModified
    });

    const templateData: CreateTemplateRequest = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      file_type: formData.file_type,
      presentation_file: selectedFile,
      demo_video: selectedVideo || undefined,
    };

    console.log('🚀 SUBMISSION - Final template data:', {
      ...templateData,
      file: templateData.file ? {
        name: templateData.file.name,
        size: templateData.file.size,
        type: templateData.file.type
      } : 'NO FILE'
    });

    try {
      const newTemplate = await uploadTemplate(templateData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        file_type: 'pptx',
      });
      setSelectedFile(null);
      setSelectedVideo(null);
      setErrors({});
      
      // Call success callback
      onSuccess?.(newTemplate);
      onClose();
    } catch (error) {
      console.error('Erreur lors de la création du template:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    console.log('🔍 Debug - File selection:', {
      filesLength: e.target.files?.length || 0,
      file: file ? {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      } : 'NO FILE SELECTED'
    });

    if (file) {
      // Validate file before setting it
      const allowedTypes = {
        pptx: ['.pptx', '.ppt'],
        docx: ['.docx', '.doc'],
        pdf: ['.pdf']
      };

      const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      console.log('🔍 Debug - File validation:', {
        extension,
        expectedType: formData.file_type,
        allowedExtensions: allowedTypes[formData.file_type as keyof typeof allowedTypes],
        fileSize: file.size
      });

      if (!allowedTypes[formData.file_type as keyof typeof allowedTypes]?.includes(extension)) {
        console.log('❌ File type validation failed');
        setErrors(prev => ({
          ...prev,
          file: `Type de fichier invalide pour ${formData.file_type.toUpperCase()}. Extensions autorisées: ${allowedTypes[formData.file_type as keyof typeof allowedTypes]?.join(', ')}`
        }));
        e.target.value = ''; // Clear the input
        return;
      }

      // Validate file size (200MB max)
      const maxSize = 200 * 1024 * 1024;
      if (file.size > maxSize) {
        console.log('❌ File size validation failed');
        setErrors(prev => ({
          ...prev,
          file: 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.'
        }));
        e.target.value = ''; // Clear the input
        return;
      }

      console.log('✅ File validation passed, setting selected file');
      setSelectedFile(file);
      // Clear file error when a valid file is selected
      if (errors.file) {
        setErrors(prev => ({ ...prev, file: '' }));
      }
    } else {
      console.log('❌ No file in selection');
    }
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate video file before setting it
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv'];
      const videoExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      if (!videoExtensions.includes(videoExtension)) {
        setErrors(prev => ({
          ...prev,
          video: `Type de vidéo invalide. Extensions autorisées: ${videoExtensions.join(', ')}`
        }));
        e.target.value = ''; // Clear the input
        return;
      }

      // Validate video file size (100MB max)
      const maxVideoSize = 100 * 1024 * 1024;
      if (file.size > maxVideoSize) {
        setErrors(prev => ({
          ...prev,
          video: 'Le fichier est trop volumineux. Taille maximale: 200MB pour les templates, 100MB pour les vidéos.'
        }));
        e.target.value = ''; // Clear the input
        return;
      }

      setSelectedVideo(file);
      // Clear video error when a valid file is selected
      if (errors.video) {
        setErrors(prev => ({ ...prev, video: '' }));
      }
    }
  };

  const handleClose = () => {
    if (uploading) {
      return; // Prevent closing during upload
    }
    
    // Reset form
    setFormData({
      title: '',
      description: '',
      file_type: 'pptx',
    });
    setSelectedFile(null);
    setSelectedVideo(null);
    setErrors({});
    clearError();
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Créer un nouveau template
          </h2>
          <button
            onClick={handleClose}
            disabled={uploading}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {uploadError && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-800 dark:text-red-200">{uploadError}</p>
              </div>
            </div>
          )}

          {/* Template Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Titre du template *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => {
                setFormData({ ...formData, title: e.target.value });
                if (errors.title) setErrors(prev => ({ ...prev, title: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                errors.title ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Ex: Présentation Business Professionnelle"
              disabled={uploading}
            />
            {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => {
                setFormData({ ...formData, description: e.target.value });
                if (errors.description) setErrors(prev => ({ ...prev, description: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
              }`}
              rows={3}
              placeholder="Description détaillée du template..."
              disabled={uploading}
            />
            {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
          </div>

          {/* Template Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type de template *
            </label>
            <select
              value={formData.file_type}
              onChange={(e) => {
                console.log('🔄 TYPE CHANGE - From:', formData.file_type, 'To:', e.target.value);
                console.log('🔄 TYPE CHANGE - Current file:', selectedFile?.name || 'No file');

                setFormData({ ...formData, file_type: e.target.value });

                // Only clear file if it doesn't match the new type
                if (selectedFile) {
                  const allowedTypes = {
                    pptx: ['.pptx', '.ppt'],
                    docx: ['.docx', '.doc'],
                    pdf: ['.pdf']
                  };
                  const extension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
                  const newTypeExtensions = allowedTypes[e.target.value as keyof typeof allowedTypes];

                  if (!newTypeExtensions?.includes(extension)) {
                    console.log('🗑️ TYPE CHANGE - Clearing file due to type mismatch');
                    setSelectedFile(null);
                  } else {
                    console.log('✅ TYPE CHANGE - File matches new type, keeping it');
                  }
                } else {
                  console.log('ℹ️ TYPE CHANGE - No file to check');
                }

                if (errors.file) setErrors(prev => ({ ...prev, file: '' }));
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
              disabled={uploading}
            >
              <option value="pptx">PowerPoint (.pptx)</option>
              <option value="docx">Word (.docx)</option>
              <option value="pdf">PDF (.pdf)</option>
            </select>
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Fichier template *
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md hover:border-primary dark:hover:border-primary transition-colors duration-200">
              <div className="space-y-1 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="flex text-sm text-gray-600 dark:text-gray-400">
                  <label htmlFor="file-upload" className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary">
                    <span>Télécharger un fichier</span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      onChange={handleFileChange}
                      accept={
                        formData.file_type === 'pptx' ? '.pptx,.ppt' :
                        formData.file_type === 'docx' ? '.docx,.doc' :
                        '.pdf'
                      }
                      disabled={uploading}
                    />
                  </label>
                  <p className="pl-1">ou glisser-déposer</p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formData.file_type === 'pptx' && 'PPTX, PPT'}
                  {formData.file_type === 'docx' && 'DOCX, DOC'}
                  {formData.file_type === 'pdf' && 'PDF'}
                </p>
              </div>
            </div>
            {selectedFile && (
              <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(selectedFile.size)}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedFile(null)}
                    disabled={uploading}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {errors.file && <p className="text-red-600 text-sm mt-1">{errors.file}</p>}
          </div>

          {/* Video Upload (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Vidéo de démonstration (optionnel)
            </label>
            <input
              type="file"
              onChange={handleVideoChange}
              accept=".mp4,.avi,.mov,.wmv"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
              disabled={uploading}
            />
            {selectedVideo && (
              <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{selectedVideo.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(selectedVideo.size)}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedVideo(null)}
                    disabled={uploading}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {errors.video && <p className="text-red-600 text-sm mt-1">{errors.video}</p>}
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Formats supportés: MP4, AVI, MOV, WMV
            </p>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              disabled={uploading}
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Template actif (visible pour les utilisateurs)
            </label>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Upload en cours...</span>
                <span className="text-primary font-medium">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              disabled={uploading}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={uploading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {uploading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Création en cours...</span>
                </>
              ) : (
                <span>Créer le template</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateTemplateModal;
