"use client";

import { useState } from 'react';
import { useTemplates, useTemplateUpload } from '@/hooks/useTemplates';
import { CreateTemplateRequest } from '@/services/templates';

/**
 * Example component demonstrating how to use the Template API service
 * This shows best practices for:
 * - Fetching templates with filters
 * - Creating new templates with file upload
 * - Error handling and loading states
 * - Using the custom hooks
 */
const TemplateExample = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'ppt',
    is_active: true,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<File | null>(null);

  // Using the templates hook with initial filters
  const {
    templates,
    loading,
    error,
    pagination,
    fetchTemplates,
    clearError,
  } = useTemplates({
    page: 1,
    per_page: 10,
    is_active: true, // Only show active templates by default
  });

  // Using the upload hook for creating templates
  const {
    uploadTemplate,
    uploadProgress,
    uploading,
    error: uploadError,
    clearError: clearUploadError,
  } = useTemplateUpload();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      alert('Veuillez sélectionner un fichier');
      return;
    }

    const templateData: CreateTemplateRequest = {
      name: formData.name,
      description: formData.description,
      type: formData.type,
      file: selectedFile,
      demo_video: selectedVideo || undefined,
      is_active: formData.is_active,
    };

    try {
      const newTemplate = await uploadTemplate(templateData);
      console.log('Template créé avec succès:', newTemplate);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        type: 'ppt',
        is_active: true,
      });
      setSelectedFile(null);
      setSelectedVideo(null);
      setShowCreateForm(false);
      
      // Refresh templates list
      fetchTemplates();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedVideo(file);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Template API - Exemple d'utilisation</h1>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <p className="text-red-800">{error}</p>
            <button
              onClick={clearError}
              className="text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {uploadError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <p className="text-red-800">{uploadError}</p>
            <button
              onClick={clearUploadError}
              className="text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Create Template Button */}
      <div className="mb-6">
        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          {showCreateForm ? 'Annuler' : 'Créer un nouveau template'}
        </button>
      </div>

      {/* Create Template Form */}
      {showCreateForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Créer un nouveau template</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom du template *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: Présentation Business"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                required
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Description du template..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="ppt">PowerPoint (.pptx)</option>
                <option value="doc">Word (.docx)</option>
                <option value="pdf">PDF (.pdf)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fichier template *
              </label>
              <input
                type="file"
                required
                onChange={handleFileChange}
                accept=".pptx,.docx,.pdf"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {selectedFile && (
                <p className="text-sm text-gray-600 mt-1">
                  Fichier sélectionné: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vidéo de démonstration (optionnel)
              </label>
              <input
                type="file"
                onChange={handleVideoChange}
                accept=".mp4,.avi,.mov"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {selectedVideo && (
                <p className="text-sm text-gray-600 mt-1">
                  Vidéo sélectionnée: {selectedVideo.name} ({(selectedVideo.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              )}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
                Template actif
              </label>
            </div>

            {/* Upload Progress */}
            {uploading && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
                <p className="text-sm text-gray-600 mt-1">
                  Upload en cours... {uploadProgress}%
                </p>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={uploading}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploading ? 'Création en cours...' : 'Créer le template'}
              </button>
              <button
                type="button"
                onClick={() => setShowCreateForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
              >
                Annuler
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Templates List */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Templates disponibles</h2>
          <button
            onClick={() => fetchTemplates()}
            className="bg-gray-100 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-200"
          >
            Actualiser
          </button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : templates.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucun template trouvé</p>
        ) : (
          <div className="space-y-3">
            {templates.map((template) => (
              <div
                key={template.id}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.description}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {template.type.toUpperCase()}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        template.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {template.is_active ? 'Actif' : 'Inactif'}
                      </span>
                      <span className="text-xs text-gray-500">
                        Créé le {new Date(template.created_at).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {template.demo_video_url && (
                      <a
                        href={template.demo_video_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Voir vidéo
                      </a>
                    )}
                    <a
                      href={template.file_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-green-600 hover:text-green-800 text-sm"
                    >
                      Télécharger
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Info */}
        {pagination && (
          <div className="mt-4 text-sm text-gray-600 text-center">
            Page {pagination.current_page} sur {pagination.last_page} 
            ({pagination.total} templates au total)
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateExample;
