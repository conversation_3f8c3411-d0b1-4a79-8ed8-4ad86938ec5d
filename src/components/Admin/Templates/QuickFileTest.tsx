"use client";

import { useState } from 'react';

/**
 * Quick test component to isolate file upload issues
 */
const QuickFileTest = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [testResult, setTestResult] = useState<string>('');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    console.log('🔍 File selected:', file);
    setSelectedFile(file || null);
  };

  const testFormData = () => {
    if (!selectedFile) {
      setTestResult('❌ No file selected');
      return;
    }

    console.log('🧪 Testing FormData creation...');
    
    const formData = new FormData();
    formData.append('name', 'Test Template');
    formData.append('description', 'Test Description');
    formData.append('type', 'ppt');
    formData.append('is_active', '1');
    formData.append('file', selectedFile);

    console.log('🔍 FormData contents:');
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`  ${key}:`, {
          name: value.name,
          size: value.size,
          type: value.type
        });
      } else {
        console.log(`  ${key}:`, value);
      }
    }

    setTestResult(`✅ FormData created successfully with file: ${selectedFile.name}`);
  };

  const testDirectUpload = async () => {
    if (!selectedFile) {
      setTestResult('❌ No file selected');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('name', 'Test Template');
      formData.append('description', 'Test Description');
      formData.append('type', 'ppt');
      formData.append('is_active', '1');
      formData.append('file', selectedFile);

      const token = localStorage.getItem('auth_token');
      
      console.log('🚀 Making direct API call...');
      
      const response = await fetch('http://localhost:8000/api/v1/templates', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData
      });

      console.log('📡 Response status:', response.status);
      
      const responseText = await response.text();
      console.log('📄 Response body:', responseText);

      if (response.ok) {
        setTestResult('✅ Upload successful!');
      } else {
        setTestResult(`❌ Upload failed: ${response.status} - ${responseText}`);
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      setTestResult(`❌ Upload error: ${error.message}`);
    }
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        Quick File Upload Test
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select File
          </label>
          <input
            type="file"
            onChange={handleFileSelect}
            accept=".pptx,.ppt,.docx,.doc,.pdf"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
          />
        </div>

        {selectedFile && (
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {selectedFile.name}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </p>
          </div>
        )}

        <div className="space-y-2">
          <button
            onClick={testFormData}
            disabled={!selectedFile}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Test FormData
          </button>
          
          <button
            onClick={testDirectUpload}
            disabled={!selectedFile}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Test Direct Upload
          </button>
        </div>

        {testResult && (
          <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
            <p className="text-sm text-gray-800 dark:text-gray-200">
              {testResult}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuickFileTest;
