"use client";
import Image from "next/image";
import Link from "next/link";
import { useI18n } from "@/i18n/context";
import SocialLinks from "./SocialLinks";
import FooterLinks from "./FooterLinks";
import FooterDecoration from "./FooterDecoration";

const Footer = () => {
  const { t, isRTL } = useI18n();
  
  // Footer link sections data
  const footerSections = [
    {
      title: t('footer.services.title'),
      links: [
        { text: t('footer.services.links.powerpoint'), href: "/services/powerpoint" },
        { text: t('footer.services.links.word'), href: "/services/document" },
        { text: t('footer.services.links.cv'), href: "/services/cv" },
        { text: t('footer.services.links.pricing'), href: "/services/pricing" },
        { text: t('footer.services.links.contactSales'), href: "/services/contact" }
      ]
    },
    {
      title: "Explore",
      links: [
        { text: "Portfolio", href: "/portfolio" },
        { text: "Templates", href: "/templates" },
        { text: "Blog", href: "/blog" },
        { text: "Resources", href: "/resources" }
      ]
    },
    {
      title: t('footer.discover.title'),
      links: [
        { text: t('footer.discover.links.documentation'), href: "/about" },
        { text: t('footer.discover.links.releases'), href: "/releases" },
        { text: t('footer.discover.links.community'), href: "/community" },
        { text: t('footer.discover.links.careers'), href: "/careers" }
      ]
    }
  ];

  // Quick links for footer header
  const quickLinks = [
    { text: t('footer.company.links.about'), href: "/about" },
    { text: t('footer.services.title'), href: "/services" },
    { text: t('footer.company.links.contact'), href: "/contact" },
    { text: t('footer.company.links.careers'), href: "/careers" }
  ];

  // Legal links for footer bottom
  const legalLinks = [
    { text: t('footer.legal.links.legal'), href: "/legal" },
    { text: t('footer.legal.links.trust'), href: "/trust" },
    { text: t('footer.legal.links.status'), href: "/status" }
  ];

  return (
    <footer className="relative bg-gradient-to-br from-[#0a0e27] via-[#1a1f3a] to-[#0f1629] pt-12 sm:pt-16 lg:pt-20 pb-8 sm:pb-12">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-[#1CBCCF]/20 to-transparent"></div>
      </div>

      <div className="container relative z-10">
        {/* Footer Header with Logo and Quick Links */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-10 sm:mb-12 pb-6 sm:pb-8 border-b border-white/10">
          <Link href="/" className="inline-block mb-4 sm:mb-0">
            <Image
              src="/images/logo.png"
              alt={`${t('header.brand')} Logo`}
              width={120}
              height={40}
              className="h-auto w-auto max-w-[120px] sm:max-w-[140px]"
            />
          </Link>
          
          {/* Quick Links in Header */}
          <div className="flex flex-wrap justify-center gap-4 sm:gap-6">
            {quickLinks.map((link, index) => (
              <Link 
                key={index}
                href={link.href} 
                className="text-gray-300 hover:text-[#1CBCCF] transition-colors duration-300 text-sm sm:text-base"
              >
                {link.text}
              </Link>
            ))}
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-12 mb-12 sm:mb-16">
          {/* Brand Section */}
          <div className="sm:col-span-2 lg:col-span-1">
            <p className="text-gray-300 text-xs sm:text-sm leading-relaxed mb-6 sm:mb-8 max-w-sm">
              {t('footer.brand.description')}
            </p>

            {/* Social Links */}
            <SocialLinks />
          </div>

          {/* Footer Link Sections */}
          {footerSections.map((section, index) => (
            <FooterLinks 
              key={index}
              title={section.title}
              links={section.links}
            />
          ))}
        </div>

        {/* Footer Bottom */}
        <div className="border-t border-white/10 pt-6 sm:pt-8 mt-12 sm:mt-16">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className={`flex flex-col sm:flex-row items-center mb-4 sm:mb-0 ${isRTL ? 'sm:space-x-reverse' : ''} sm:space-x-6 space-y-2 sm:space-y-0`}>
              <p className="text-gray-400 text-xs sm:text-sm text-center sm:text-left">
                {t('footer.legal.copyright')}
              </p>
              <div className="flex items-center space-x-4 sm:space-x-6">
                {legalLinks.map((link, index) => (
                  <Link 
                    key={index}
                    href={link.href} 
                    className="text-gray-400 hover:text-[#1CBCCF] text-xs sm:text-sm transition-colors duration-300 mobile-touch-button"
                  >
                    {link.text}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Large Brand Watermark */}
        <div className="absolute bottom-0 left-0 right-0 overflow-hidden pointer-events-none">
          <div className="text-[8rem] sm:text-[12rem] md:text-[16rem] lg:text-[20rem] font-bold text-white/5 leading-none text-center select-none">
            {t('header.brand')}
          </div>
        </div>
      </div>
      
      {/* Background Decorations */}
      <FooterDecoration />
    </footer>
  );
};

export default Footer;
