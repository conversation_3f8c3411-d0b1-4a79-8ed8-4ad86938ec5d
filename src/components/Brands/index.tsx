"use client";

// Slogan section for Sademy services
import { useI18n } from "@/i18n/context";

const Brands = () => {
  const { t, isRTL } = useI18n();
  return (
    <section className="pt-16">
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="w-full px-4">
            <div className="flex items-center justify-center rounded-2xl bg-gradient-to-r from-[#1CBCCF]/10 via-white to-[#2E86C1]/10 dark:from-[#1CBCCF]/20 dark:via-gray-dark dark:to-[#2E86C1]/20 px-8 py-12 shadow-lg backdrop-blur-sm border border-[#1CBCCF]/20 sm:px-10 md:px-[50px] md:py-[50px] xl:p-[60px] 2xl:px-[80px] 2xl:py-[70px]">
              <div className={`text-center max-w-4xl ${isRTL ? 'text-right' : 'text-left'} md:text-center`}>
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-[#1CBCCF] via-[#2E86C1] to-[#1CBCCF] bg-clip-text text-transparent mb-4 leading-tight">
                  &ldquo;{t('slogan.title')}&rdquo;
                </h2>
                <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 font-medium italic">
                  {t('slogan.subtitle')}
                </p>
                <div className="mt-6 flex items-center justify-center space-x-2">
                  <div className="h-1 w-12 bg-gradient-to-r from-[#1CBCCF] to-[#2E86C1] rounded-full"></div>
                  <div className="h-2 w-2 bg-[#1CBCCF] rounded-full animate-pulse"></div>
                  <div className="h-1 w-12 bg-gradient-to-r from-[#2E86C1] to-[#1CBCCF] rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Brands;