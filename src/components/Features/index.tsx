"use client";

import SectionTitle from "../Common/SectionTitle";
import SingleFeature from "./SingleFeature";
import featuresData from "./featuresData";
import { useI18n } from "@/i18n/context";
import { useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Features = () => {
  const { t, isRTL } = useI18n();
  const featuresItems = t('features.items');
  const { containerRef, getItemProps } = useStaggeredAnimation(featuresItems.length, {
    staggerDelay: 150,
    threshold: 0.1
  });

  return (
    <>
      <section id="benefits" className="relative mobile-section-padding py-12 sm:py-16 md:py-20 lg:py-28">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-900/20"></div>

        <div className="container relative">
          {/* Section Header */}
          <div className="mb-12 sm:mb-16 text-center">
            <SectionTitle
              title={t('features.title')}
              paragraph={t('features.subtitle')}
              center
              width="800px"
              mb="40px"
            />
          </div>

          {/* Benefits Grid - Responsive layout */}
          <div 
            ref={containerRef} 
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10"
          >
            {featuresItems.map((feature, index) => (
              <div
                key={index}
                {...getItemProps(index)}
                className="transform transition-all duration-500 hover:translate-y-[-8px]"
              >
                <div className="h-full bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 group">
                  <div className="p-6 sm:p-8 flex flex-col items-center text-center h-full">
                    {/* Icon with animated background */}
                    <div className="relative mb-6">
                      <div className="w-16 h-16 flex items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300">
                        <div className="absolute inset-0 rounded-full bg-primary/5 dark:bg-primary/10 animate-pulse" style={{ animationDuration: '3s' }}></div>
                        <div className="relative z-10 text-primary dark:text-primary-light">
                          {featuresData[index]?.icon}
                        </div>
                      </div>
                    </div>
                    
                    {/* Title with hover effect */}
                    <h3 className="text-xl sm:text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-primary dark:group-hover:text-primary-light transition-colors duration-300">
                      {feature.title}
                    </h3>
                    
                    {/* Description with improved readability */}
                    <p className="text-body-color dark:text-body-color-dark text-base leading-relaxed flex-grow">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Bottom Call to Action */}
          <div className={`mt-12 sm:mt-16 text-center ${isRTL ? 'text-right' : 'text-left'} sm:text-center`}>
            <div className="mx-auto max-w-2xl px-4">
              <p className="text-base sm:text-lg font-medium text-body-color dark:text-body-color-dark">
                {t('features.cta.description')}
              </p>
              <div className="mt-4 sm:mt-6">
                <button className="rounded-full bg-primary px-6 py-3 sm:px-8 sm:py-3 text-sm sm:text-base font-semibold text-white transition-all duration-300 hover:bg-primary/90 hover:shadow-lg mobile-touch-button w-full sm:w-auto">
                  {t('features.cta.button')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Features;
