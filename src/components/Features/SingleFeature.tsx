import { Feature } from "@/types/feature";
import React from "react";

const SingleFeature = ({ feature }: { feature: Feature }) => {
  const { icon, title, paragraph } = feature;
  
  return (
    <div className="h-full bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 group">
      <div className="p-6 sm:p-8 flex flex-col items-center text-center h-full">
        {/* Icon with animated background */}
        <div className="relative mb-6">
          <div className="w-16 h-16 flex items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300">
            <div className="absolute inset-0 rounded-full bg-primary/5 dark:bg-primary/10 animate-pulse" style={{ animationDuration: '3s' }}></div>
            <div className="relative z-10 text-primary dark:text-primary-light">
              {icon}
            </div>
          </div>
        </div>
        
        {/* Title with hover effect */}
        <h3 className="text-xl sm:text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-primary dark:group-hover:text-primary-light transition-colors duration-300">
          {title}
        </h3>
        
        {/* Description with improved readability */}
        <p className="text-body-color dark:text-body-color-dark text-base leading-relaxed flex-grow">
          {paragraph}
        </p>
      </div>
    </div>
  );
};

export default SingleFeature;
