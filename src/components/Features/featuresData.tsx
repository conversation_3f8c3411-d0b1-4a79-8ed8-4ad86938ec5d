import { Feature } from "@/types/feature";

const featuresData: Feature[] = [
  {
    id: 1,
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40" className="fill-current">
        <path
          opacity="0.5"
          d="M20 0C8.95 0 0 8.95 0 20C0 31.05 8.95 40 20 40C31.05 40 40 31.05 40 20C40 8.95 31.05 0 20 0ZM20 36C11.16 36 4 28.84 4 20C4 11.16 11.16 4 20 4C28.84 4 36 11.16 36 20C36 28.84 28.84 36 20 36Z"
        />
        <path d="M20 8C15.58 8 12 11.58 12 16V18C12 18.55 12.45 19 13 19H27C27.55 19 28 18.55 28 18V16C28 11.58 24.42 8 20 8ZM24 15H16V16C16 13.79 17.79 12 20 12C22.21 12 24 13.79 24 16V15ZM13 21C12.45 21 12 21.45 12 22V30C12 30.55 12.45 31 13 31H27C27.55 31 28 30.55 28 30V22C28 21.45 27.55 21 27 21H13ZM26 29H14V23H26V29ZM18 25H22V27H18V25Z"/>
      </svg>
    ),
    title: "Fast Turnaround",
    paragraph:
      "We understand the importance of deadlines. Our efficient processes ensure your documents are delivered promptly without compromising on quality.",
  },
  {
    id: 2,
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40" className="fill-current">
        <path
          opacity="0.5"
          d="M20 0C8.95 0 0 8.95 0 20C0 31.05 8.95 40 20 40C31.05 40 40 31.05 40 20C40 8.95 31.05 0 20 0ZM20 36C11.16 36 4 28.84 4 20C4 11.16 11.16 4 20 4C28.84 4 36 11.16 36 20C36 28.84 28.84 36 20 36Z"
        />
        <path d="M20 8L25.09 13.09L32 12L30.91 18.91L36 24L30.91 29.09L32 36L25.09 34.91L20 40L14.91 34.91L8 36L9.09 29.09L4 24L9.09 18.91L8 12L14.91 13.09L20 8ZM20 14C16.69 14 14 16.69 14 20C14 23.31 16.69 26 20 26C23.31 26 26 23.31 26 20C26 16.69 23.31 14 20 14ZM20 22C19.45 22 19 21.55 19 21V19C19 18.45 19.45 18 20 18C20.55 18 21 18.45 21 19V21C21 21.55 20.55 22 20 22Z"/>
      </svg>
    ),
    title: "Professional Quality",
    paragraph:
      "Our team of expert designers and formatters ensures every document meets the highest standards of professionalism and visual appeal.",
  },
  {
    id: 3,
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40" className="fill-current">
        <path
          opacity="0.5"
          d="M20 0C8.95 0 0 8.95 0 20C0 31.05 8.95 40 20 40C31.05 40 40 31.05 40 20C40 8.95 31.05 0 20 0ZM20 36C11.16 36 4 28.84 4 20C4 11.16 11.16 4 20 4C28.84 4 36 11.16 36 20C36 28.84 28.84 36 20 36Z"
        />
        <path d="M20 8C15.58 8 12 11.58 12 16C12 20.42 15.58 24 20 24C24.42 24 28 20.42 28 16C28 11.58 24.42 8 20 8ZM20 20C17.79 20 16 18.21 16 16C16 13.79 17.79 12 20 12C22.21 12 24 13.79 24 16C24 18.21 22.21 20 20 20ZM14 26C12.9 26 12 26.9 12 28V30C12 31.1 12.9 32 14 32H26C27.1 32 28 31.1 28 30V28C28 26.9 27.1 26 26 26H14ZM14 30V28H26V30H14Z"/>
      </svg>
    ),
    title: "Affordable Pricing",
    paragraph:
      "Receive top-tier document design services at competitive rates, providing exceptional value for your investment.",
  },
  {
    id: 4,
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40" className="fill-current">
        <path
          opacity="0.5"
          d="M20 0C8.95 0 0 8.95 0 20C0 31.05 8.95 40 20 40C31.05 40 40 31.05 40 20C40 8.95 31.05 0 20 0ZM20 36C11.16 36 4 28.84 4 20C4 11.16 11.16 4 20 4C28.84 4 36 11.16 36 20C36 28.84 28.84 36 20 36Z"
        />
        <path d="M20 8C15.58 8 12 11.58 12 16C12 20.42 15.58 24 20 24C24.42 24 28 20.42 28 16C28 11.58 24.42 8 20 8ZM20 20C17.79 20 16 18.21 16 16C16 13.79 17.79 12 20 12C22.21 12 24 13.79 24 16C24 18.21 22.21 20 20 20ZM8 26C6.9 26 6 26.9 6 28V30C6 31.1 6.9 32 8 32H12C13.1 32 14 31.1 14 30V28C14 26.9 13.1 26 12 26H8ZM26 26C24.9 26 24 26.9 24 28V30C24 31.1 24.9 32 26 32H32C33.1 32 34 31.1 34 30V28C34 26.9 33.1 26 32 26H26ZM17 26C15.9 26 15 26.9 15 28V30C15 31.1 15.9 32 17 32H23C24.1 32 25 31.1 25 30V28C25 26.9 24.1 26 23 26H17Z"/>
      </svg>
    ),
    title: "Personalized Service",
    paragraph:
      "We tailor our services to your specific needs, ensuring a customized approach that perfectly aligns with your vision and requirements.",
  },

];
export default featuresData;
