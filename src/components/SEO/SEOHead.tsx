import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'service';
  publishedTime?: string;
  modifiedTime?: string;
}

export function generateSEOMetadata({
  title = "Sademy - Services Professionnels de Design de Documents",
  description = "Services professionnels de design : présentations PowerPoint pour soutenances, formatage Word et création de CV. Solutions expertes pour étudiants et professionnels.",
  keywords = [],
  image = "/images/logo.png",
  url = "https://sademy.com",
  type = "website",
  publishedTime,
  modifiedTime
}: SEOProps): Metadata {
  
  const defaultKeywords = [
    "PowerPoint professionnel",
    "présentation soutenance", 
    "formatage Word",
    "création CV",
    "design document",
    "services étudiants",
    "Sademy",
    "Maroc"
  ];

  const allKeywords = [...defaultKeywords, ...keywords];
  
  const fullTitle = title.includes('Sademy') ? title : `${title} | Sademy`;
  
  return {
    title: fullTitle,
    description,
    keywords: allKeywords,
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: "Sademy",
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "fr_FR",
      type: type === "service" ? "website" : type,
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [image],
    },
    alternates: {
      canonical: url,
    },
  };
}

// Structured data generators for different page types
export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Sademy",
    "description": "Services professionnels de design de documents",
    "url": "https://sademy.com",
    "logo": "https://sademy.com/images/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["French", "Arabic"]
    },
    "areaServed": {
      "@type": "Country", 
      "name": "Morocco"
    },
    "serviceType": [
      "PowerPoint Presentation Design",
      "Word Document Formatting",
      "CV Creation"
    ]
  };
}

export function generateServiceSchema(serviceName: string, description: string) {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": serviceName,
    "description": description,
    "provider": {
      "@type": "Organization",
      "name": "Sademy",
      "url": "https://sademy.com"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Morocco"
    },
    "audience": {
      "@type": "Audience", 
      "audienceType": ["Students", "Professionals"]
    }
  };
}
