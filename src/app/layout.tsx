import Footer from "@/components/Footer";
import Header from "@/components/Header";
import ScrollToTop from "@/components/ScrollToTop";
import WhatsAppButton from "@/components/WhatsAppButton";
import { Inter } from "next/font/google";
import "../styles/index.css";
import { Providers } from "./providers";
import type { Metadata } from 'next';
import ConditionalLayout from "@/components/ConditionalLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: 'Sademy - Services Professionnels de Design de Documents',
    template: '%s | Sademy'
  },
  description: 'Sademy offre des services professionnels de design de documents : présentations PowerPoint pour soutenances, formatage de documents Word et création de CV. Solutions expertes pour étudiants et professionnels.',
  keywords: [
    'PowerPoint',
    'présentation',
    'soutenance',
    'thèse',
    'mémoire',
    'Word',
    'document',
    'formatage',
    'CV',
    'curriculum vitae',
    'design professionnel',
    'étudiant',
    'académique',
    '<PERSON><PERSON><PERSON>'
  ],
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: 'Sademy',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'fr_FR',
    url: 'https://sademy.com',
    siteName: 'Sademy',
    title: 'Sademy - Services Professionnels de Design de Documents',
    description: 'Transformez vos documents avec les services professionnels de Sademy : présentations PowerPoint pour soutenances, formatage Word et création de CV. Design expert pour une communication impactante.',
    images: [
      {
        url: '/images/logo.png',
        width: 1200,
        height: 630,
        alt: 'Sademy - Services de Design Professionnel',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sademy - Services Professionnels de Design de Documents',
    description: 'Services experts en PowerPoint, Word et CV pour étudiants et professionnels. Design professionnel pour vos soutenances et documents.',
    images: ['/images/logo.png'],
    creator: '@sademy',
  },
  icons: {
    icon: [
      { url: '/images/logo.png', sizes: '32x32', type: 'image/png' },
      { url: '/images/logo.png', sizes: '16x16', type: 'image/png' },
    ],
    apple: [
      { url: '/images/logo.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/images/logo.png',
  },
  manifest: '/manifest.json',
  alternates: {
    canonical: 'https://sademy.com',
    languages: {
      'fr-FR': 'https://sademy.com/fr',
      'ar-MA': 'https://sademy.com/ar',
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Sademy",
    "description": "Services professionnels de design de documents : présentations PowerPoint, formatage Word et création de CV",
    "url": "https://sademy.com",
    "logo": "https://sademy.com/images/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["French", "Arabic"]
    },
    "areaServed": {
      "@type": "Country",
      "name": "Morocco"
    },
    "serviceType": [
      "PowerPoint Presentation Design",
      "Word Document Formatting",
      "CV Creation",
      "Academic Document Design"
    ],
    "audience": {
      "@type": "Audience",
      "audienceType": ["Students", "Professionals", "Academics"]
    }
  };

  return (
    <html suppressHydrationWarning lang="fr">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`bg-[#FCFCFC] dark:bg-black ${inter.className}`}>
        <Providers>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
        </Providers>
      </body>
    </html>
  );
}

