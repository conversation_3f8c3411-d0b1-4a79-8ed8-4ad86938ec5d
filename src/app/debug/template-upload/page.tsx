"use client";

import TemplateUploadTest from '@/components/Admin/Templates/TemplateUploadTest';
import QuickFileTest from '@/components/Admin/Templates/QuickFileTest';

export default function DebugTemplateUploadPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Template Upload Debug Page
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Use this page to debug template upload issues. Check the browser console for detailed logs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <QuickFileTest />
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
              Quick Debug Steps:
            </h3>
            <ol className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1 list-decimal list-inside">
              <li>Select a file in the Quick Test</li>
              <li>Click "Test FormData" to verify file is captured</li>
              <li>Click "Test Direct Upload" to bypass the modal</li>
              <li>Check browser console for detailed logs</li>
              <li>If direct upload works, the issue is in the modal</li>
            </ol>
          </div>
        </div>

        <TemplateUploadTest />
      </div>
    </div>
  );
}
