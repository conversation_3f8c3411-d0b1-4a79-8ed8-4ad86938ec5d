"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { AuthService } from "@/services/auth";
import { useI18n } from "@/i18n/context";

interface Project {
  id: number;
  title: string;
  client_name: string;
  client_email: string;
  status: 'assigned' | 'in_progress' | 'completed' | 'delivered';
  deadline: string;
  type: 'powerpoint' | 'word' | 'cv';
  created_at: string;
}

const CollaboratorDashboard = () => {
  const { t } = useI18n();
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(AuthService.getUser());

  // Check authentication and user role
  useEffect(() => {
    if (!AuthService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    const userData = AuthService.getUser();
    if (!userData?.roles.includes('Collaborator')) {
      router.push('/');
      return;
    }

    setUser(userData);
    loadProjects();
  }, [router]);

  const loadProjects = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      // const response = await api.get('/collaborator/projects');
      // setProjects(response.data);
      
      // Mock data for now
      const mockProjects: Project[] = [
        {
          id: 1,
          title: "Présentation de Thèse - Informatique",
          client_name: "Ahmed Benali",
          client_email: "<EMAIL>",
          status: "assigned",
          deadline: "2024-01-20",
          type: "powerpoint",
          created_at: "2024-01-15"
        },
        {
          id: 2,
          title: "CV Professionnel",
          client_name: "Fatima Zahra",
          client_email: "<EMAIL>",
          status: "in_progress",
          deadline: "2024-01-18",
          type: "cv",
          created_at: "2024-01-12"
        },
        {
          id: 3,
          title: "Document Word - Rapport",
          client_name: "Mohamed Alami",
          client_email: "<EMAIL>",
          status: "completed",
          deadline: "2024-01-16",
          type: "word",
          created_at: "2024-01-10"
        }
      ];
      
      setProjects(mockProjects);
    } catch (error) {
      console.error('Error loading projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      assigned: { color: 'bg-blue-100 text-blue-800', text: 'Assigné' },
      in_progress: { color: 'bg-yellow-100 text-yellow-800', text: 'En cours' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Terminé' },
      delivered: { color: 'bg-purple-100 text-purple-800', text: 'Livré' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.assigned;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      powerpoint: "/images/powerpointe.png",
      word: "/images/Word.png",
      cv: "/images/cv-icon.png" // You might need to add this icon
    };
    
    return icons[type as keyof typeof icons] || "/images/powerpointe.png";
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      powerpoint: "PowerPoint",
      word: "Word Document",
      cv: "CV Professionnel"
    };
    
    return labels[type as keyof typeof labels] || type;
  };

  const handleStatusUpdate = async (projectId: number, newStatus: string) => {
    try {
      // TODO: Replace with actual API call
      // await api.patch(`/collaborator/projects/${projectId}`, { status: newStatus });
      
      // Update local state for now
      setProjects(prev => 
        prev.map(project => 
          project.id === projectId 
            ? { ...project, status: newStatus as Project['status'] }
            : project
        )
      );
    } catch (error) {
      console.error('Error updating project status:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <section className="relative z-10 overflow-hidden pt-36 pb-16 md:pb-20 lg:pt-[180px] lg:pb-28">
        <div className="container">
          <div className="text-center mb-12">
            <h1 className="mb-4 text-3xl font-bold text-black sm:text-4xl dark:text-white">
              Tableau de Bord Collaborateur
            </h1>
            <p className="text-body-color text-lg">
              Gérez vos projets assignés et livrez des créations de qualité
            </p>
          </div>

          {/* User Info */}
          {user && (
            <div className="mb-8 p-6 bg-white dark:bg-gray-dark rounded-lg shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center text-lg font-medium">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-black dark:text-white">{user.name}</h2>
                    <p className="text-body-color">{user.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-body-color">Projets actifs</p>
                  <p className="text-2xl font-bold text-primary">
                    {projects.filter(p => p.status === 'assigned' || p.status === 'in_progress').length}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Projects Grid */}
          {projects.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {projects.map((project) => (
                <div
                  key={project.id}
                  className="bg-white dark:bg-gray-dark rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Project Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <Image
                        src={getTypeIcon(project.type)}
                        alt={getTypeLabel(project.type)}
                        width={40}
                        height={40}
                        className="rounded"
                      />
                      <div>
                        <h3 className="text-lg font-semibold text-black dark:text-white line-clamp-1">
                          {project.title}
                        </h3>
                        <p className="text-sm text-body-color">{getTypeLabel(project.type)}</p>
                      </div>
                    </div>
                    {getStatusBadge(project.status)}
                  </div>

                  {/* Client Info */}
                  <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <p className="text-sm font-medium text-black dark:text-white">Client:</p>
                    <p className="text-sm text-body-color">{project.client_name}</p>
                    <p className="text-sm text-body-color">{project.client_email}</p>
                  </div>

                  {/* Project Details */}
                  <div className="mb-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-body-color">Échéance:</span>
                      <span className="font-medium text-black dark:text-white">
                        {new Date(project.deadline).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-body-color">Créé le:</span>
                      <span className="font-medium text-black dark:text-white">
                        {new Date(project.created_at).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    {project.status === 'assigned' && (
                      <button
                        onClick={() => handleStatusUpdate(project.id, 'in_progress')}
                        className="flex-1 bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                      >
                        Commencer
                      </button>
                    )}
                    {project.status === 'in_progress' && (
                      <button
                        onClick={() => handleStatusUpdate(project.id, 'completed')}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                      >
                        Marquer terminé
                      </button>
                    )}
                    {project.status === 'completed' && (
                      <button
                        onClick={() => handleStatusUpdate(project.id, 'delivered')}
                        className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                      >
                        Livrer au client
                      </button>
                    )}
                    <button className="flex-1 border border-primary text-primary hover:bg-primary hover:text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm">
                      Voir détails
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Image
                src="/images/powerpointe.png"
                alt="No projects"
                width={120}
                height={120}
                className="mx-auto mb-6 opacity-50"
              />
              <h3 className="text-xl font-semibold text-black dark:text-white mb-2">
                Aucun projet assigné
              </h3>
              <p className="text-body-color">
                Vous n'avez pas encore de projets assignés. Contactez l'administrateur.
              </p>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default CollaboratorDashboard;
