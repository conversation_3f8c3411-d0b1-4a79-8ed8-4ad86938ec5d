"use client";

import Link from "next/link";
import { useI18n } from "@/i18n/context";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { TemplateService, Template } from "@/services/templates";
import { AuthService } from "@/services/auth";


const DocumentServicePage = () => {
  const { t, isRTL, locale } = useI18n();
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // Get user role for conditional rendering (only on client side)
  const [user, setUser] = useState<any>(null);
  const [userRole, setUserRole] = useState("User");
  const [canDownload, setCanDownload] = useState(false);

  // Check user role on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isAuthenticated = AuthService.isAuthenticated();
      const userData = AuthService.getUser();
      setUser(userData);
      const role = userData?.roles?.[0] || "User";
      setUserRole(role);

      // Only show download section for Admin & Collaborator
      // Hide for: not authenticated, User role, or no roles
      const hasDownloadAccess = isAuthenticated &&
        userData?.roles &&
        (userData.roles.includes('Admin') || userData.roles.includes('Collaborator'));

      setCanDownload(hasDownloadAccess);
    }
  }, []);

  // Fetch templates from API
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await TemplateService.getPublicTemplates({
          file_type: 'docx',
          is_active: true
        });
        setTemplates(response.data.templates);
      } catch (err: any) {
        setError(err.message);
        console.error('Error fetching templates:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Document categories
  const categories = [
    { id: "all", name: { fr: "Tous", ar: "الكل" } },
    { id: "thesis", name: { fr: "Thèses", ar: "أطروحات" } },
    { id: "reports", name: { fr: "Rapports", ar: "تقارير" } },
    { id: "articles", name: { fr: "Articles", ar: "مقالات" } },
    { id: "cv", name: { fr: "CV", ar: "سيرة ذاتية" } }
  ];



  // Helper functions

  const copyTemplateLink = async (template: Template) => {
    try {
      const link = template.presentation_file_url || '';
      await navigator.clipboard.writeText(link);
      // You could add a toast notification here
      alert('Lien copié dans le presse-papiers!');
    } catch (err) {
      console.error('Failed to copy link:', err);
      alert('Erreur lors de la copie du lien');
    }
  };

  const downloadVideo = async (template: Template) => {
    try {
      if (template.demo_video_url) {
        const link = document.createElement('a');
        link.href = template.demo_video_url;
        link.download = `${template.title}_demo.mp4`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (err) {
      console.error('Failed to download video:', err);
      alert('Erreur lors du téléchargement de la vidéo');
    }
  };

  // Filter templates by category (if needed)
  const filteredTemplates = templates;

  return (
    <div className="min-h-screen bg-[#1a1f2e]">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 rounded-full bg-secondary"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 rounded-full bg-primary"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-secondary"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-secondary/20 rounded-2xl mb-8">
              <svg className="w-10 h-10 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Mise en Page <span className="text-secondary">Word</span>
            </h1>

            {/* Description */}
            <p className="text-xl text-gray-300 mb-12 leading-relaxed">
              Donnez une présentation professionnelle à vos documents Word. Formatage expert selon les normes académiques pour thèses, mémoires, rapports et articles scientifiques.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">800+</div>
                <div className="text-gray-400 text-sm">Documents Formatés</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">25K+</div>
                <div className="text-gray-400 text-sm">Pages Mises en Forme</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">100%</div>
                <div className="text-gray-400 text-sm">Normes Respectées</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">48h</div>
                <div className="text-gray-400 text-sm">Délai Moyen</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Documents Catalog Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          {/* Category Filters */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-secondary text-white shadow-lg'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name[locale] || category.name.fr}
              </button>
            ))}
          </motion.div>

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-20">
              <div className="text-red-400 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <p className="text-gray-400 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-secondary hover:bg-secondary/90 text-white rounded-lg transition-colors"
              >
                Réessayer
              </button>
            </div>
          )}

          {/* Documents Grid */}
          {!loading && !error && (
            <motion.div
              className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {filteredTemplates.map((template, index) => (
              <motion.div
                key={template.id}
                className="group bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden border border-gray-700/50 hover:border-secondary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-secondary/10 catalog-card"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* Document Preview */}
                <div className="relative aspect-[3/4] bg-white overflow-hidden">
                  {/* Demo Video Preview - Embedded Player */}
                  {template.has_demo_video && template.demo_video_url ? (
                    <div className="relative w-full h-full bg-black">
                      <video
                        className="w-full h-full object-cover"
                        controls
                        preload="metadata"
                      >
                        <source src={template.demo_video_url} type="video/mp4" />
                        <p className="text-white p-4 text-center">
                          Votre navigateur ne supporte pas la lecture vidéo.
                        </p>
                      </video>
                    </div>
                  ) : (
                    /* Document Page Mockup */
                    <div className="absolute inset-4 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                      {/* Document Header */}
                      <div className="p-4 border-b border-gray-200">
                        <div className="h-3 bg-gray-800 rounded mb-2"></div>
                        <div className="h-2 bg-gray-400 rounded w-2/3"></div>
                      </div>

                      {/* Document Content */}
                      <div className="p-4 space-y-2">
                        <div className="h-2 bg-gray-600 rounded w-full"></div>
                        <div className="h-2 bg-gray-600 rounded w-5/6"></div>
                        <div className="h-2 bg-gray-600 rounded w-4/5"></div>
                        <div className="h-2 bg-gray-600 rounded w-full"></div>
                        <div className="h-2 bg-gray-600 rounded w-3/4"></div>
                        <div className="py-1"></div>
                        <div className="h-2 bg-gray-600 rounded w-full"></div>
                        <div className="h-2 bg-gray-600 rounded w-4/5"></div>
                        <div className="h-2 bg-gray-600 rounded w-5/6"></div>
                        <div className="h-2 bg-gray-600 rounded w-2/3"></div>
                      </div>
                    </div>
                  )}

                  {/* Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary text-white">
                      {template.file_type?.toUpperCase() || 'DOCX'}
                    </span>
                  </div>

                  {/* Hover Overlay for non-video templates */}
                  {(!template.has_demo_video || !template.demo_video_url) && (
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                      <div className="text-center text-white">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                          <polyline points="14,2 14,8 20,8"/>
                          <line x1="16" y1="13" x2="8" y2="13"/>
                          <line x1="16" y1="17" x2="8" y2="17"/>
                          <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        <p className="text-sm font-medium">Aperçu Document</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-lg font-bold text-white mb-3 line-clamp-2 group-hover:text-secondary transition-colors duration-300">
                    {template.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2 leading-relaxed">
                    {template.description}
                  </p>

                  {/* Template Info */}
                  <div className="flex items-center justify-between mb-4 text-sm text-gray-400">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                        </svg>
                        <span>{template.file_type?.toUpperCase()}</span>
                      </div>
                      {template.has_demo_video && (
                        <div className="flex items-center gap-1">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z"/>
                          </svg>
                          <span>Vidéo démo</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                    {/* Date */}
                    <div className="flex items-center gap-1 text-gray-400 text-sm">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                      </svg>
                      <span>{new Date(template.created_at).toLocaleDateString('fr-FR')}</span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-2">
                      {/* Role-based buttons */}
                      {canDownload ? (
                        /* Admin & Collaborator: Download Video Button */
                        template.has_demo_video && template.demo_video_url && (
                          <button
                            onClick={() => downloadVideo(template)}
                            className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary/90 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25"
                            title="Télécharger la vidéo"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                            </svg>
                            Télécharger Vidéo
                          </button>
                        )
                      ) : (
                        /* User: Copy Template Link Button */
                        <button
                          onClick={() => copyTemplateLink(template)}
                          className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary/90 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25"
                          title="Copier le lien du template"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                          </svg>
                          Copier Lien
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
          )}

          {/* Empty State */}
          {!loading && !error && filteredTemplates.length === 0 && (
            <div className="text-center py-20">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                </svg>
              </div>
              <p className="text-gray-400 mb-4">Aucun template disponible pour le moment</p>
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-20 bg-gray-900/30">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Pourquoi Choisir Notre Service de Mise en Page ?
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="w-16 h-16 bg-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Normes Académiques</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Respect strict des normes universitaires et standards de rédaction
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-16 h-16 bg-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Table des Matières</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Génération automatique avec liens hypertextes et numérotation
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-16 h-16 bg-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Bibliographie</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Formatage des références selon les normes APA, IEEE, MLA
              </p>
            </motion.div>

            {/* Feature 4 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-16 h-16 bg-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9H21ZM19 21H5V3H13V9H19V21Z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Styles Personnalisés</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Création de styles cohérents pour titres, paragraphes et citations
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-4xl mx-auto text-center bg-gradient-to-r from-secondary/10 to-primary/10 rounded-3xl p-12 border border-secondary/20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Donnez Vie à Votre Travail Académique
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Transformez votre contenu en document Word professionnel. Mise en page experte, formatage selon les normes académiques, et présentation impeccable garantie.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/#contact"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-secondary hover:bg-secondary/90 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25"
                >
                  Commencer Mon Projet
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                  </svg>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/about"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg"
                >
                  Voir Tous les Services
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                  </svg>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>


    </div>
  );
};

export default DocumentServicePage;
