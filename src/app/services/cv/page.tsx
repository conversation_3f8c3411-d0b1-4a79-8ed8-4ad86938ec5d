"use client"
import Link from "next/link"
import { useI18n } from "@/i18n/context"
import { motion } from "framer-motion"
import { useState } from "react"

const CVServicePage = () => {
  const { t, isRTL, locale } = useI18n()
  const [selectedCategory, setSelectedCategory] = useState("all")

  // CV categories
  const categories = [
    { id: "all", name: { fr: "Tous", ar: "الكل" } },
    { id: "student", name: { fr: "Étudiant", ar: "طالب" } },
    { id: "professional", name: { fr: "Professionnel", ar: "مهني" } },
    { id: "creative", name: { fr: "C<PERSON><PERSON><PERSON>", ar: "إبداعي" } },
    { id: "academic", name: { fr: "Académique", ar: "أكاديمي" } }
  ]

  // Enhanced CV catalog with more details
  const cvData = [
    {
      id: 1,
      title: { fr: "CV Étudiant - Ingénierie Informatique", ar: "سيرة ذاتية طالب - هندسة الحاسوب" },
      description: { fr: "CV moderne pour étudiant en informatique avec sections optimisées pour stages et projets académiques.", ar: "سيرة ذاتية حديثة لطالب الحاسوب مع أقسام محسنة للتدريب والمشاريع الأكاديمية." },
      category: "student",
      experience: "0-2 ans",
      format: "A4",
      pages: 1,
      features: ["ATS-Friendly", "Design Moderne", "Sections Optimisées"],
      badge: { fr: "POPULAIRE", ar: "شائع" }
    },
    {
      id: 2,
      title: { fr: "CV Professionnel - Marketing Digital", ar: "سيرة ذاتية مهنية - التسويق الرقمي" },
      description: { fr: "CV professionnel avec design épuré et mise en valeur des compétences marketing et résultats quantifiés.", ar: "سيرة ذاتية مهنية مع تصميم أنيق وإبراز مهارات التسويق والنتائج المقيسة." },
      category: "professional",
      experience: "3-8 ans",
      format: "A4",
      pages: 2,
      features: ["Résultats Quantifiés", "Design Premium", "Multi-pages"],
      badge: { fr: "PREMIUM", ar: "مميز" }
    },
    {
      id: 3,
      title: { fr: "CV Créatif - Design Graphique", ar: "سيرة ذاتية إبداعية - التصميم الجرافيكي" },
      description: { fr: "CV créatif avec éléments visuels attractifs, parfait pour les métiers du design et de la création.", ar: "سيرة ذاتية إبداعية مع عناصر بصرية جذابة، مثالية لمهن التصميم والإبداع." },
      category: "creative",
      experience: "2-5 ans",
      format: "A4",
      pages: 1,
      features: ["Design Unique", "Éléments Visuels", "Portfolio Intégré"],
      badge: { fr: "CRÉATIF", ar: "إبداعي" }
    },
    {
      id: 4,
      title: { fr: "CV Académique - Recherche Scientifique", ar: "سيرة ذاتية أكاديمية - البحث العلمي" },
      description: { fr: "CV académique complet avec publications, conférences et expérience de recherche détaillée.", ar: "سيرة ذاتية أكاديمية كاملة مع المنشورات والمؤتمرات وخبرة البحث المفصلة." },
      category: "academic",
      experience: "5+ ans",
      format: "A4",
      pages: 3,
      features: ["Publications", "Conférences", "Format Académique"],
      badge: { fr: "ACADÉMIQUE", ar: "أكاديمي" }
    },
    {
      id: 5,
      title: { fr: "CV Professionnel - Gestion de Projet", ar: "سيرة ذاتية مهنية - إدارة المشاريع" },
      description: { fr: "CV structuré pour chef de projet avec focus sur les méthodologies et réalisations concrètes.", ar: "سيرة ذاتية منظمة لمدير المشاريع مع التركيز على المنهجيات والإنجازات الملموسة." },
      category: "professional",
      experience: "5-10 ans",
      format: "A4",
      pages: 2,
      features: ["Méthodologies", "Certifications", "Leadership"],
      badge: { fr: "EXPERT", ar: "خبير" }
    },
    {
      id: 6,
      title: { fr: "CV Étudiant - Sciences Médicales", ar: "سيرة ذاتية طالب - العلوم الطبية" },
      description: { fr: "CV médical pour étudiant avec sections spécialisées pour stages cliniques et formations médicales.", ar: "سيرة ذاتية طبية للطالب مع أقسام متخصصة للتدريب السريري والتكوين الطبي." },
      category: "student",
      experience: "0-1 an",
      format: "A4",
      pages: 1,
      features: ["Stages Cliniques", "Formations Médicales", "Compétences Techniques"],
      badge: { fr: "MÉDICAL", ar: "طبي" }
    }
  ]

  // Get localized CVs
  const cvs = cvData.map(item => ({
    ...item,
    title: item.title[locale] || item.title.fr,
    description: item.description[locale] || item.description.fr,
    badge: item.badge[locale] || item.badge.fr
  }))

  // Filter CVs by category
  const filteredCVs = selectedCategory === "all"
    ? cvs
    : cvs.filter(cv => cv.category === selectedCategory)

  return (
    <div className="min-h-screen bg-[#1a1f2e]">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 rounded-full bg-primary"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 rounded-full bg-secondary"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-primary to-secondary"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl mb-8">
              <svg className="w-10 h-10 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9H21ZM19 21H5V3H13V9H19V21Z"/>
              </svg>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              CV <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Professionnel</span>
            </h1>

            {/* Description */}
            <p className="text-xl text-gray-300 mb-12 leading-relaxed">
              Créez un CV qui ouvre les portes. Design moderne, contenu optimisé ATS, et stratégie personnalisée pour maximiser vos chances de décrocher l&apos;emploi de vos rêves.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">1200+</div>
                <div className="text-gray-400 text-sm">CV Créés</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">87%</div>
                <div className="text-gray-400 text-sm">Entretiens Obtenus</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">72h</div>
                <div className="text-gray-400 text-sm">Délai Maximum</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">4.8★</div>
                <div className="text-gray-400 text-sm">Satisfaction Client</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CV Catalog Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          {/* Category Filters */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name[locale] || category.name.fr}
              </button>
            ))}
          </motion.div>

          {/* CV Grid */}
          <motion.div
            className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {filteredCVs.map((cv, index) => (
              <motion.div
                key={cv.id}
                className="group bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden border border-gray-700/50 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10 catalog-card"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* CV Preview */}
                <div className="relative aspect-[3/4] bg-white overflow-hidden">
                  {/* CV Mockup */}
                  <div className="absolute inset-4 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                    {/* CV Header */}
                    <div className="p-4 bg-gradient-to-r from-primary/10 to-secondary/10">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
                        <div>
                          <div className="h-3 bg-gray-800 rounded mb-1 w-20"></div>
                          <div className="h-2 bg-gray-500 rounded w-16"></div>
                        </div>
                      </div>
                    </div>

                    {/* CV Content */}
                    <div className="p-4 space-y-3">
                      <div className="space-y-1">
                        <div className="h-2 bg-primary rounded w-16"></div>
                        <div className="h-1.5 bg-gray-600 rounded w-full"></div>
                        <div className="h-1.5 bg-gray-600 rounded w-4/5"></div>
                      </div>
                      <div className="space-y-1">
                        <div className="h-2 bg-secondary rounded w-20"></div>
                        <div className="h-1.5 bg-gray-600 rounded w-full"></div>
                        <div className="h-1.5 bg-gray-600 rounded w-3/4"></div>
                      </div>
                      <div className="space-y-1">
                        <div className="h-2 bg-primary rounded w-18"></div>
                        <div className="h-1.5 bg-gray-600 rounded w-5/6"></div>
                      </div>
                    </div>
                  </div>

                  {/* Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-secondary text-white">
                      {cv.badge}
                    </span>
                  </div>

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div className="text-center text-white">
                      <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                      <p className="text-sm font-medium">Aperçu CV</p>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-lg font-bold text-white mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-300">
                    {cv.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2 leading-relaxed">
                    {cv.description}
                  </p>

                  {/* CV Details */}
                  <div className="flex items-center justify-between mb-4 text-sm text-gray-400">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9H21ZM19 21H5V3H13V9H19V21Z"/>
                        </svg>
                        <span>{cv.pages} page{cv.pages > 1 ? 's' : ''}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>{cv.experience}</span>
                      </div>
                    </div>
                    <span className="text-xs">{cv.format}</span>
                  </div>

                  {/* Features */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {cv.features.slice(0, 2).map((feature, idx) => (
                      <span key={idx} className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full">
                        {feature}
                      </span>
                    ))}
                    {cv.features.length > 2 && (
                      <span className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full">
                        +{cv.features.length - 2}
                      </span>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                    {/* Category */}
                    <div className="flex items-center gap-1 text-gray-400 text-sm">
                      <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
                      <span className="capitalize">{cv.category}</span>
                    </div>

                    {/* Action Button */}
                    <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-primary/25">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                      Choisir
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-20 bg-gray-900/30">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Pourquoi Nos CV Font la Différence ?
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-primary/30 group-hover:to-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Optimisation ATS</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                CV conçu pour franchir les filtres automatiques des recruteurs
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-primary/30 group-hover:to-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Impact Visuel</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Design moderne qui attire l&apos;attention et marque les esprits
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-primary/30 group-hover:to-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Contenu Stratégique</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Rédaction ciblée selon votre secteur et poste visé
              </p>
            </motion.div>

            {/* Feature 4 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-primary/30 group-hover:to-secondary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9H21ZM19 21H5V3H13V9H19V21Z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Suivi Personnalisé</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Accompagnement complet jusqu&apos;à l&apos;obtention de votre emploi
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-4xl mx-auto text-center bg-gradient-to-r from-primary/10 to-secondary/10 rounded-3xl p-12 border border-primary/20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Votre Carrière Commence Ici
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Ne laissez pas un CV ordinaire freiner vos ambitions. Investissez dans un CV professionnel qui ouvre les portes et transforme vos candidatures en entretiens.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/#contact"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-primary/25"
                >
                  Commencer Mon CV
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                  </svg>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/about"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg"
                >
                  En Savoir Plus
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                  </svg>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default CVServicePage
