"use client"
import Link from "next/link"
import { useI18n } from "@/i18n/context"
import { motion } from "framer-motion"
import { useState, useEffect, useMemo } from "react"
import { TemplateService, Template } from "@/services/templates"
import { AuthService } from "@/services/auth"
import Image from "next/image"

const PowerPointServicePage = () => {
  const { t, isRTL, locale } = useI18n()
  const [selectedCategory, setSelectedCategory] = useState("all")

  const [downloading, setDownloading] = useState(false)
  const [apiTemplates, setApiTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Role-based access control
  const [user, setUser] = useState<any>(null)
  const [canDownload, setCanDownload] = useState(false)

  // Check user role on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isAuthenticated = AuthService.isAuthenticated()
      const userData = AuthService.getUser()
      setUser(userData)

      // Only show download section for Admin & Collaborator
      // Hide for: not authenticated, User role, or no roles
      const hasDownloadAccess = isAuthenticated &&
        userData?.roles &&
        (userData.roles.includes('Admin') || userData.roles.includes('Collaborator'))

      setCanDownload(hasDownloadAccess)
    }
  }, [])

  // Fetch templates from public API
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await TemplateService.getPublicTemplates({
          file_type: 'pptx',
          is_active: true,
          per_page: 50
        })
        setApiTemplates(response.data.templates)
      } catch (err: any) {
        setError(err.message)
        console.error('Error fetching templates:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchTemplates()
  }, [])

  // Template categories
  const categories = [
    { id: "all", name: { fr: "Tous", ar: "الكل" } },
    { id: "thesis", name: { fr: "Soutenance", ar: "مناقشة" } },
    { id: "pfe", name: { fr: "PFE", ar: "مشروع التخرج" } },
    { id: "master", name: { fr: "Master", ar: "ماجستير" } },
    { id: "doctorate", name: { fr: "Doctorat", ar: "دكتوراه" } }
  ]



  // Handle template download (Admin & Collaborator only)
  const handleTemplateDownload = async (templateId: number, templateName: string) => {
    if (!canDownload) {
      alert('Accès refusé. Seuls les administrateurs et collaborateurs peuvent télécharger les présentations.')
      return
    }

    try {
      setDownloading(true)
      const blob = await TemplateService.downloadPresentation(templateId)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${templateName}.pptx`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up
      window.URL.revokeObjectURL(url)
    } catch (error: any) {
      console.error('Download failed:', error)
      alert(`Erreur lors du téléchargement: ${error.message}`)
    } finally {
      setDownloading(false)
    }
  }

  // Copy template link function
  const copyTemplateLink = async (template: Template) => {
    try {
      const link = template.presentation_file_url || '';
      await navigator.clipboard.writeText(link);
      alert('Lien copié dans le presse-papiers!');
    } catch (err) {
      console.error('Failed to copy link:', err);
      alert('Erreur lors de la copie du lien');
    }
  };

  // Fallback templates data for demo purposes
  const fallbackTemplatesData = [
    {
      id: 1,
      name: "Soutenance de Thèse - Médecine",
      description: "Template PowerPoint médical professionnel avec graphiques et animations pour soutenance de thèse en médecine.",
      category: "thesis",
      rating: 4.9,
      downloads: 298,
      size: "18.5 MB",
      date: "15 Décembre 2024",
      previewImage: "/images/powerpoint/thesis-medecine.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    },
    {
      id: 2,
      name: "Présentation PFE - Architecture",
      description: "Template PowerPoint architectural avec plans et visualisations 3D. Parfait pour les projets de fin d'études en architecture.",
      category: "pfe",
      rating: 4.7,
      downloads: 187,
      size: "15.2 MB",
      date: "10 Décembre 2024",
      previewImage: "/images/powerpoint/pfe-architecture.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    },
    {
      id: 3,
      name: "Soutenance Master - Économie",
      description: "Présentation PowerPoint économique avec graphiques financiers et analyses de marché pour master en économie.",
      category: "master",
      rating: 4.8,
      downloads: 203,
      size: "11.3 MB",
      date: "5 Décembre 2024",
      previewImage: "/images/powerpoint/master-economie.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    },
    {
      id: 4,
      name: "Présentation Doctorat - Informatique",
      description: "Template PowerPoint technique avec diagrammes UML et algorithmes pour soutenance de doctorat en informatique.",
      category: "doctorate",
      rating: 5.0,
      downloads: 156,
      size: "22.1 MB",
      date: "28 Novembre 2024",
      previewImage: "/images/powerpoint/doctorat-informatique.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    },
    {
      id: 5,
      name: "Soutenance PFE - Ingénierie",
      description: "Template PowerPoint ingénierie avec schémas techniques et simulations. Idéal pour les projets d'ingénierie.",
      category: "pfe",
      rating: 4.6,
      downloads: 167,
      size: "19.4 MB",
      date: "20 Novembre 2024",
      previewImage: "/images/powerpoint/pfe-ingenierie.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    },
    {
      id: 6,
      name: "Conférence Académique",
      description: "Template PowerPoint académique élégant pour conférences et présentations scientifiques professionnelles.",
      category: "thesis",
      rating: 4.8,
      downloads: 234,
      size: "14.7 MB",
      date: "15 Novembre 2024",
      previewImage: "/images/powerpoint/conference-academique.jpg",
      badge: "PPTX",
      demo_video_url: "/images/powerpoint/Screencast From 2025-02-11 11-31-07.mp4"
    }
  ]

  // Use API templates if available, otherwise use fallback data
  const templates = apiTemplates && apiTemplates.length > 0 ? apiTemplates : fallbackTemplatesData

  // Map category names for filtering
  const getCategoryFromTemplate = (template: any) => {
    if (template.name?.toLowerCase().includes('thèse') || template.name?.toLowerCase().includes('thesis')) return 'thesis'
    if (template.name?.toLowerCase().includes('pfe')) return 'pfe'
    if (template.name?.toLowerCase().includes('master')) return 'master'
    if (template.name?.toLowerCase().includes('doctorat') || template.name?.toLowerCase().includes('doctorate')) return 'doctorate'
    return 'all'
  }

  // Filter templates by category
  const filteredTemplates = selectedCategory === "all"
    ? templates
    : templates.filter(template => getCategoryFromTemplate(template) === selectedCategory)

  // Calculate file size from bytes if needed
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  return (
    <div className="min-h-screen bg-[#1a1f2e]">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 rounded-full bg-primary"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 rounded-full bg-secondary"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-primary"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/20 rounded-2xl mb-8">
              <svg className="w-10 h-10 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.5 6h11V4h-11c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h11v-2h-11V6z"/>
                <path d="M15 8v8l5-4z"/>
              </svg>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Présentations <span className="text-primary">PowerPoint</span>
            </h1>

            {/* Description */}
            <p className="text-xl text-gray-300 mb-12 leading-relaxed">
              Créez des présentations PowerPoint professionnelles qui captivent votre audience. Soutenances, PFE, conférences - nous transformons vos idées en présentations percutantes avec des vidéos de démonstration.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-6 mb-16">
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">300+</div>
                <div className="text-gray-400 text-sm">Présentations Créées</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">5000+</div>
                <div className="text-gray-400 text-sm">Slides Conçues</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">98%</div>
                <div className="text-gray-400 text-sm">Soutenances Réussies</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">50+</div>
                <div className="text-gray-400 text-sm">Vidéos Démo</div>
              </motion.div>
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">4.9★</div>
                <div className="text-gray-400 text-sm">Satisfaction Client</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>



      {/* Templates Catalog Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          {/* Section Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Catalogue de Templates PowerPoint
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Explorez notre collection de templates professionnels avec vidéos de démonstration
            </p>
          </motion.div>

          {/* Category Filters */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name[locale] || category.name.fr}
              </button>
            ))}
          </motion.div>

          {/* Loading State */}
          {loading && (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 text-white">
                <svg className="w-6 h-6 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2v4m0 12v4m10-10h-4M6 12H2m15.364-7.364l-2.828 2.828M9.464 9.464L6.636 6.636m12.728 12.728l-2.828-2.828M9.464 14.536l-2.828 2.828"/>
                </svg>
                <span className="text-lg">Chargement des templates...</span>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 max-w-md mx-auto">
                <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <h3 className="text-lg font-semibold text-red-400 mb-2">Erreur de chargement</h3>
                <p className="text-red-300 text-sm mb-4">{error}</p>
                <p className="text-gray-400 text-sm">Affichage des templates de démonstration</p>
              </div>
            </div>
          )}

          {/* Templates Grid */}
          {!loading && (
            <motion.div
              className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
            {filteredTemplates.map((template, index) => (
              <motion.div
                key={template.id}
                className="group bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden border border-gray-700/50 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10 catalog-card"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* Preview Image or Video */}
                <div className="relative aspect-video bg-gradient-to-br from-gray-700 to-gray-800 overflow-hidden">
                  {/* Demo Video Player - Embedded if available */}
                  {template.demo_video_url ? (
                    <div className="relative w-full h-full bg-black">
                      <video
                        className="w-full h-full object-cover"
                        controls
                        preload="metadata"
                        poster={template.previewImage}
                      >
                        <source src={template.demo_video_url} type="video/mp4" />
                        <p className="text-white p-4 text-center">
                          Votre navigateur ne supporte pas la lecture vidéo.
                        </p>
                      </video>
                    </div>
                  ) : template.previewImage ? (
                    /* Template preview image */
                    <Image
                      src={template.previewImage}
                      alt={`Preview of ${template.name || template.title}`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    /* Placeholder */
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                      <div className="text-center text-white/80">
                        <svg className="w-16 h-16 mx-auto mb-4 opacity-60" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8.5 6h11V4h-11c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h11v-2h-11V6z"/>
                          <path d="M15 8v8l5-4z"/>
                        </svg>
                        <p className="text-sm font-medium">Template Preview</p>
                      </div>
                    </div>
                  )}

                  {/* Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-500 text-white">
                      {template.badge || 'PPTX'}
                    </span>
                  </div>

                  {/* Demo Video Badge */}
                  {template.demo_video_url && (
                    <div className="absolute top-4 right-4">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500 text-white">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z" />
                        </svg>
                        DEMO
                      </span>
                    </div>
                  )}

                  {/* Hover Overlay - Only for non-video templates */}
                  {!template.demo_video_url && (
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                      <div className="text-center text-white">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        <p className="text-sm font-medium">Aperçu Template</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-lg font-bold text-white mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-300">
                    {template.name || template.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2 leading-relaxed">
                    {template.description}
                  </p>

                  {/* Stats Row */}
                  <div className="flex items-center justify-between mb-4">
                    {/* Rating */}
                    <div className="flex items-center gap-1">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${i < Math.floor(template.rating || 4.5) ? 'text-yellow-400' : 'text-gray-600'}`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                          </svg>
                        ))}
                      </div>
                      <span className="text-sm font-medium text-gray-300 ml-1">({template.rating || '4.5'})</span>
                    </div>

                    {/* Size */}
                    <span className="text-xs text-gray-500">
                      {template.size || (template.file_size ? formatFileSize(template.file_size) : '10 MB')}
                    </span>
                  </div>

                  {/* Action Buttons Row */}
                  <div className="flex items-center gap-2 mb-4">
                    {/* Copy Link Button */}
                    <button
                      onClick={() => copyTemplateLink(template)}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-all duration-300"
                      title="Copier le lien de la présentation"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                      </svg>
                      Copy Link
                    </button>
                  </div>

                  {/* Footer - Only show for Admin & Collaborator */}
                  {canDownload === true && (
                    <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                      {/* Downloads */}
                      <div className="flex items-center gap-1 text-gray-400">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                        </svg>
                        <span className="text-sm">{template.downloads || '0'}</span>
                      </div>

                      {/* Download Button */}
                      <button
                        onClick={() => handleTemplateDownload(template.id, template.name || template.title)}
                        disabled={downloading}
                        className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/90 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-primary/25"
                      >
                        {downloading ? (
                          <>
                            <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2v4m0 12v4m10-10h-4M6 12H2m15.364-7.364l-2.828 2.828M9.464 9.464L6.636 6.636m12.728 12.728l-2.828-2.828M9.464 14.536l-2.828 2.828"/>
                            </svg>
                            Téléchargement...
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                            </svg>
                            Télécharger
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
            </motion.div>
          )}

          {/* Load More Button */}
          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <button className="inline-flex items-center gap-2 px-8 py-4 bg-primary hover:bg-primary/90 text-white font-medium rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-primary/25">
              Charger Plus de Templates PPT
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </button>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-20 bg-gray-900/30">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Pourquoi Choisir Nos Présentations PowerPoint ?
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="w-16 h-16 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Design Professionnel</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Présentations élégantes adaptées à votre domaine d&apos;étude
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-16 h-16 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Contenu Structuré</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Organisation logique et claire de vos idées et arguments
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-16 h-16 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Animations Intégrées</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Transitions fluides et animations pour captiver l&apos;audience
              </p>
            </motion.div>

            {/* Feature 4 */}
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-16 h-16 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/30 transition-all duration-300">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9H21ZM19 21H5V3H13V9H19V21Z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">Support Complet</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Accompagnement et révisions jusqu&apos;à votre soutenance
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-4xl mx-auto text-center bg-gradient-to-r from-primary/10 to-secondary/10 rounded-3xl p-12 border border-primary/20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Prêt à Réussir Votre Soutenance ?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Transformez votre travail en présentation PowerPoint percutante. Nos experts créent des présentations qui marquent les esprits et garantissent votre succès.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/#contact"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-primary hover:bg-primary/90 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-primary/25"
                >
                  Commencer Mon Projet
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                  </svg>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/about"
                  className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-full transition-all duration-300 hover:shadow-lg"
                >
                  En Savoir Plus
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                  </svg>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>


    </div>
  )
}

export default PowerPointServicePage
