"use client";

import { useI18n } from "@/i18n/context";
import Contact from "@/components/Contact";
import { Metadata } from "next";

const TranslationDemo = () => {
  const { t, locale, isRTL } = useI18n();

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Translation Demo
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
            Current Language: <span className="font-semibold text-primary">{locale.toUpperCase()}</span>
          </p>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-2xl mx-auto">
            <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">
              Translation Examples
            </h2>
            <div className="space-y-4 text-left">
              <div>
                <strong>Contact Title:</strong> {t('contact.title')}
              </div>
              <div>
                <strong>Contact Description:</strong> {t('contact.description')}
              </div>
              <div>
                <strong>Name Label:</strong> {t('contact.form.name.label')}
              </div>
              <div>
                <strong>Email Label:</strong> {t('contact.form.email.label')}
              </div>
              <div>
                <strong>Message Label:</strong> {t('contact.form.message.label')}
              </div>
              <div>
                <strong>Submit Button:</strong> {t('contact.form.submit')}
              </div>
            </div>
          </div>
        </div>
        
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">
            Contact Component with Translation
          </h2>
          <Contact />
        </div>
      </div>
    </div>
  );
};

export default TranslationDemo;
