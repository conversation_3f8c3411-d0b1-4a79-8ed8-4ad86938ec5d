"use client";

import { useState, useEffect } from "react";
import { AuthService } from "@/services/auth";
import Link from "next/link";

const AuthTestPage = () => {
  const [authStatus, setAuthStatus] = useState({
    isAuthenticated: false,
    user: null,
    token: null
  });

  useEffect(() => {
    const checkAuth = () => {
      const isAuthenticated = AuthService.isAuthenticated();
      const user = AuthService.getUser();
      const token = AuthService.getToken();
      
      setAuthStatus({
        isAuthenticated,
        user,
        token
      });
    };

    checkAuth();
    
    // Listen for storage changes (login/logout in other tabs)
    window.addEventListener('storage', checkAuth);
    return () => window.removeEventListener('storage', checkAuth);
  }, []);

  const handleLogout = async () => {
    try {
      await AuthService.logout();
      setAuthStatus({
        isAuthenticated: false,
        user: null,
        token: null
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Authentication Test Page
          </h1>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Authentication Status
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  authStatus.isAuthenticated 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {authStatus.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </span>
              </div>
              
              {authStatus.user && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">User ID:</span>
                    <span className="text-gray-900 dark:text-white">{authStatus.user.id}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                    <span className="text-gray-900 dark:text-white">{authStatus.user.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Email:</span>
                    <span className="text-gray-900 dark:text-white">{authStatus.user.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                    <span className="text-gray-900 dark:text-white">{authStatus.user.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Roles:</span>
                    <div className="flex space-x-2">
                      {authStatus.user.roles.map((role, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-sm"
                        >
                          {role}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Phone Verified:</span>
                    <span className={`px-2 py-1 rounded text-sm ${
                      authStatus.user.is_phone_verified 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      {authStatus.user.is_phone_verified ? 'Verified' : 'Not Verified'}
                    </span>
                  </div>
                </div>
              )}
              
              {authStatus.token && (
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Token:</span>
                    <span className="text-gray-900 dark:text-white text-sm font-mono break-all">
                      {authStatus.token.substring(0, 50)}...
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Role-Based Access Test
            </h2>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Admin Access</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Only users with 'Admin' role can access
                  </p>
                  {authStatus.user?.roles.includes('Admin') ? (
                    <Link 
                      href="/admin/dashboard"
                      className="inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors"
                    >
                      Go to Admin Dashboard
                    </Link>
                  ) : (
                    <span className="inline-block bg-gray-400 text-white px-4 py-2 rounded text-sm">
                      Access Denied
                    </span>
                  )}
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Collaborator Access</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Only users with 'Collaborator' role can access
                  </p>
                  {authStatus.user?.roles.includes('Collaborator') ? (
                    <Link 
                      href="/collaborator/dashboard"
                      className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors"
                    >
                      Go to Collaborator Dashboard
                    </Link>
                  ) : (
                    <span className="inline-block bg-gray-400 text-white px-4 py-2 rounded text-sm">
                      Access Denied
                    </span>
                  )}
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">User Access</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Only users with 'User' role can access
                  </p>
                  {authStatus.user?.roles.includes('User') ? (
                    <Link 
                      href="/my-presentations"
                      className="inline-block bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm transition-colors"
                    >
                      Go to My Presentations
                    </Link>
                  ) : (
                    <span className="inline-block bg-gray-400 text-white px-4 py-2 rounded text-sm">
                      Access Denied
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Actions
            </h2>
            
            <div className="flex space-x-4">
              {!authStatus.isAuthenticated ? (
                <Link 
                  href="/login"
                  className="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded transition-colors"
                >
                  Go to Login
                </Link>
              ) : (
                <button 
                  onClick={handleLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded transition-colors"
                >
                  Logout
                </button>
              )}
              
              <Link 
                href="/"
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded transition-colors"
              >
                Go to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthTestPage;
