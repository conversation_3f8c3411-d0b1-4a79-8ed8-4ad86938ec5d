"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { AuthService } from "@/services/auth";
import { useI18n } from "@/i18n/context";

interface Presentation {
  id: number;
  title: string;
  date: string;
  status: 'completed' | 'in_progress' | 'pending';
  file_url?: string;
  thumbnail_url?: string;
}

const MyPresentationsPage = () => {
  const { t } = useI18n();
  const router = useRouter();
  const [presentations, setPresentations] = useState<Presentation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(AuthService.getUser());

  // Check authentication and user role
  useEffect(() => {
    if (!AuthService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    const userData = AuthService.getUser();
    if (!userData?.roles.includes('User')) {
      router.push('/');
      return;
    }

    setUser(userData);
    loadPresentations();
  }, [router]);

  const loadPresentations = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      // const response = await api.get('/user/presentations');
      // setPresentations(response.data);
      
      // Mock data for now
      const mockPresentations: Presentation[] = [
        {
          id: 1,
          title: "Présentation de Thèse - Informatique",
          date: "2024-01-15",
          status: "completed",
          file_url: "/presentations/thesis-presentation.pptx",
          thumbnail_url: "/images/presentation-thumb-1.jpg"
        },
        {
          id: 2,
          title: "Projet de Fin d'Études",
          date: "2024-01-10",
          status: "in_progress",
          thumbnail_url: "/images/presentation-thumb-2.jpg"
        },
        {
          id: 3,
          title: "Présentation Marketing",
          date: "2024-01-05",
          status: "pending",
          thumbnail_url: "/images/presentation-thumb-3.jpg"
        }
      ];
      
      setPresentations(mockPresentations);
    } catch (error) {
      console.error('Error loading presentations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { color: 'bg-green-100 text-green-800', text: 'Terminé' },
      in_progress: { color: 'bg-yellow-100 text-yellow-800', text: 'En cours' },
      pending: { color: 'bg-gray-100 text-gray-800', text: 'En attente' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <section className="relative z-10 overflow-hidden pt-36 pb-16 md:pb-20 lg:pt-[180px] lg:pb-28">
        <div className="container">
          <div className="text-center mb-12">
            <h1 className="mb-4 text-3xl font-bold text-black sm:text-4xl dark:text-white">
              Mes Présentations
            </h1>
            <p className="text-body-color text-lg">
              Retrouvez toutes vos présentations PowerPoint créées par notre équipe
            </p>
          </div>

          {/* User Info */}
          {user && (
            <div className="mb-8 p-6 bg-white dark:bg-gray-dark rounded-lg shadow-lg">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center text-lg font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-black dark:text-white">{user.name}</h2>
                  <p className="text-body-color">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Presentations Grid */}
          {presentations.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {presentations.map((presentation) => (
                <div
                  key={presentation.id}
                  className="bg-white dark:bg-gray-dark rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Thumbnail */}
                  <div className="h-48 bg-gray-200 dark:bg-gray-700 relative">
                    {presentation.thumbnail_url ? (
                      <Image
                        src={presentation.thumbnail_url}
                        alt={presentation.title}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Fallback to PowerPoint icon if image fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : null}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Image
                        src="/images/powerpointe.png"
                        alt="PowerPoint"
                        width={64}
                        height={64}
                        className="opacity-80"
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-semibold text-black dark:text-white line-clamp-2">
                        {presentation.title}
                      </h3>
                      {getStatusBadge(presentation.status)}
                    </div>
                    
                    <p className="text-body-color text-sm mb-4">
                      Date: {new Date(presentation.date).toLocaleDateString('fr-FR')}
                    </p>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      {presentation.file_url && presentation.status === 'completed' && (
                        <a
                          href={presentation.file_url}
                          download
                          className="flex-1 bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                        >
                          Télécharger
                        </a>
                      )}
                      <button className="flex-1 border border-primary text-primary hover:bg-primary hover:text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm">
                        Voir détails
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Image
                src="/images/powerpointe.png"
                alt="No presentations"
                width={120}
                height={120}
                className="mx-auto mb-6 opacity-50"
              />
              <h3 className="text-xl font-semibold text-black dark:text-white mb-2">
                Aucune présentation trouvée
              </h3>
              <p className="text-body-color mb-6">
                Vous n'avez pas encore de présentations. Contactez-nous pour créer votre première présentation.
              </p>
              <Link
                href="/#contact"
                className="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-md transition-colors duration-200"
              >
                Nous contacter
              </Link>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default MyPresentationsPage;
