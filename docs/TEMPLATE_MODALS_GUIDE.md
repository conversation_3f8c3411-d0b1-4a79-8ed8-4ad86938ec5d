# Template Management Modals Guide

This guide covers the complete modal system for template management in the admin dashboard.

## 📋 Overview

The template management system includes three main modal components:

1. **CreateTemplateModal** - For creating new templates
2. **EditTemplateModal** - For editing existing templates  
3. **DeleteConfirmModal** - For safely deleting templates

All modals are fully integrated with the admin templates page and include comprehensive error handling, validation, and user feedback.

## 🎯 Modal Components

### 1. CreateTemplateModal

**Location**: `src/components/Admin/Templates/CreateTemplateModal.tsx`

**Features**:
- ✅ **Complete form validation** with real-time feedback
- ✅ **File upload** with drag-and-drop support
- ✅ **Progress tracking** during upload
- ✅ **File type validation** (PPT, DOC, PDF)
- ✅ **File size validation** (50MB for templates, 100MB for videos)
- ✅ **Optional video upload** for demo videos
- ✅ **Responsive design** with dark mode support

**Usage**:
```typescript
<CreateTemplateModal
  isOpen={isCreateModalOpen}
  onClose={() => setIsCreateModalOpen(false)}
  onSuccess={(template) => {
    showSuccess('Template créé avec succès!', `Le template "${template.name}" a été créé.`);
    setIsCreateModalOpen(false);
    fetchTemplates(filters);
  }}
/>
```

### 2. EditTemplateModal

**Location**: `src/components/Admin/Templates/EditTemplateModal.tsx`

**Features**:
- ✅ **Pre-populated form** with existing template data
- ✅ **Optional file replacement** (keeps existing file if not changed)
- ✅ **Current file display** with download link
- ✅ **Video management** (view current, replace optional)
- ✅ **Form validation** with error handling
- ✅ **Loading states** during submission

**Usage**:
```typescript
{editingTemplate && (
  <EditTemplateModal
    template={editingTemplate}
    isOpen={!!editingTemplate}
    onClose={() => setEditingTemplate(null)}
    onSuccess={(template) => {
      showSuccess('Template mis à jour!', `Le template "${template.name}" a été mis à jour.`);
      setEditingTemplate(null);
      fetchTemplates(filters);
    }}
  />
)}
```

### 3. DeleteConfirmModal

**Location**: `src/components/Admin/Templates/DeleteConfirmModal.tsx`

**Features**:
- ✅ **Safety confirmation** requiring user to type "supprimer"
- ✅ **Template information display** with details
- ✅ **Warning messages** about irreversible action
- ✅ **Template statistics** (creation date, download status)
- ✅ **Loading state** during deletion
- ✅ **Comprehensive UI** with icons and visual feedback

**Usage**:
```typescript
{deletingTemplate && (
  <DeleteConfirmModal
    template={deletingTemplate}
    isOpen={!!deletingTemplate}
    onClose={() => setDeletingTemplate(null)}
    onConfirm={handleDelete}
  />
)}
```

## 🎨 UI/UX Features

### Design Principles:
- **Consistent styling** across all modals
- **Dark mode support** for all components
- **Responsive design** that works on all screen sizes
- **Accessibility features** with proper ARIA labels
- **Loading states** and progress indicators
- **Error states** with clear messaging

### Visual Elements:
- **Modal overlays** with backdrop blur
- **Smooth animations** for show/hide
- **Color-coded actions** (green for create, blue for edit, red for delete)
- **Icon usage** for better visual hierarchy
- **Progress bars** for file uploads
- **File preview** with size information

### User Experience:
- **Prevent accidental actions** with confirmation dialogs
- **Real-time validation** with immediate feedback
- **Clear error messages** with actionable guidance
- **Progress tracking** for long operations
- **Auto-dismiss** for non-critical notifications

## 🔧 Technical Implementation

### Form Validation Example:
```typescript
const validateForm = () => {
  const newErrors: Record<string, string> = {};

  if (!formData.name.trim()) {
    newErrors.name = 'Le nom du template est requis';
  }

  if (!selectedFile) {
    newErrors.file = 'Veuillez sélectionner un fichier';
  } else {
    // File type validation
    const allowedTypes = {
      ppt: ['.pptx', '.ppt'],
      doc: ['.docx', '.doc'],
      pdf: ['.pdf']
    };
    
    const extension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
    if (!allowedTypes[formData.type]?.includes(extension)) {
      newErrors.file = `Type de fichier invalide pour ${formData.type.toUpperCase()}`;
    }

    // File size validation (50MB max)
    const maxSize = 50 * 1024 * 1024;
    if (selectedFile.size > maxSize) {
      newErrors.file = 'La taille du fichier ne doit pas dépasser 50MB';
    }
  }

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};
```

### File Upload with Progress:
```typescript
const { uploadTemplate, uploadProgress, uploading } = useTemplateUpload();

// Progress indicator in UI
{uploading && (
  <div className="space-y-2">
    <div className="flex items-center justify-between text-sm">
      <span className="text-gray-600 dark:text-gray-400">Upload en cours...</span>
      <span className="text-primary font-medium">{uploadProgress}%</span>
    </div>
    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
      <div
        className="bg-primary h-2 rounded-full transition-all duration-300"
        style={{ width: `${uploadProgress}%` }}
      ></div>
    </div>
  </div>
)}
```

### Safety Confirmation:
```typescript
const [confirmText, setConfirmText] = useState('');
const isConfirmValid = confirmText.toLowerCase() === 'supprimer';

// Confirmation input
<input
  type="text"
  value={confirmText}
  onChange={(e) => setConfirmText(e.target.value)}
  placeholder="Tapez 'supprimer' pour confirmer"
  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
/>

// Submit button (disabled until confirmed)
<button
  onClick={handleConfirm}
  disabled={!isConfirmValid}
  className="bg-red-600 text-white px-4 py-2 rounded-md disabled:opacity-50"
>
  Supprimer définitivement
</button>
```

## 🔒 Security & Validation

### Client-Side Validation:
- **Required field validation** for all mandatory inputs
- **File type checking** with extension validation
- **File size limits** to prevent oversized uploads
- **Real-time validation** with immediate user feedback
- **Form state management** to prevent invalid submissions

### Safety Measures:
- **Confirmation dialogs** for destructive actions
- **Loading states** to prevent double-submissions
- **Error boundaries** to handle unexpected errors
- **Input sanitization** for text fields
- **File validation** before upload

### User Protection:
- **Clear warnings** for irreversible actions
- **Detailed information** about consequences
- **Multiple confirmation steps** for critical operations
- **Escape routes** (cancel buttons always available)
- **Progress feedback** for long operations

## 📱 Responsive Design

### Mobile Optimization:
- **Touch-friendly** button sizes and spacing
- **Responsive layouts** that adapt to screen size
- **Scrollable content** for long forms
- **Optimized typography** for readability
- **Gesture support** for file uploads

### Accessibility:
- **Keyboard navigation** support
- **Screen reader compatibility** with ARIA labels
- **High contrast** support
- **Focus management** for modal interactions
- **Semantic HTML** structure

## 🎯 Integration with Admin Page

The modals are fully integrated with the admin templates page:

```typescript
const AdminTemplatesPageContent = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [deletingTemplate, setDeletingTemplate] = useState<Template | null>(null);
  
  const { showSuccess, showError } = useToast();

  // Modal handlers with toast notifications
  const handleCreateSuccess = (template) => {
    showSuccess('Template créé avec succès!', `Le template "${template.name}" a été créé.`);
    setIsCreateModalOpen(false);
    fetchTemplates(filters);
  };

  const handleEditSuccess = (template) => {
    showSuccess('Template mis à jour!', `Le template "${template.name}" a été mis à jour.`);
    setEditingTemplate(null);
    fetchTemplates(filters);
  };

  const handleDeleteSuccess = () => {
    showSuccess('Template supprimé', 'Le template a été supprimé avec succès.');
    setDeletingTemplate(null);
    fetchTemplates(filters);
  };

  // Render modals
  return (
    <div>
      {/* Page content */}
      
      <CreateTemplateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />
      
      {editingTemplate && (
        <EditTemplateModal
          template={editingTemplate}
          isOpen={!!editingTemplate}
          onClose={() => setEditingTemplate(null)}
          onSuccess={handleEditSuccess}
        />
      )}
      
      {deletingTemplate && (
        <DeleteConfirmModal
          template={deletingTemplate}
          isOpen={!!deletingTemplate}
          onClose={() => setDeletingTemplate(null)}
          onConfirm={handleDeleteSuccess}
        />
      )}
    </div>
  );
};

// Wrap with ToastProvider for notifications
const AdminTemplatesPage = () => {
  return (
    <ToastProvider>
      <AdminTemplatesPageContent />
    </ToastProvider>
  );
};
```

## 🚀 Usage Examples

### Opening Modals:
```typescript
// Create new template
<button onClick={() => setIsCreateModalOpen(true)}>
  Nouveau Template
</button>

// Edit existing template
<button onClick={() => setEditingTemplate(template)}>
  Modifier
</button>

// Delete template
<button onClick={() => setDeletingTemplate(template)}>
  Supprimer
</button>
```

### Handling Success/Error:
```typescript
const handleSuccess = (template) => {
  // Show success notification
  showSuccess('Opération réussie!', 'Le template a été traité avec succès.');
  
  // Close modal
  setIsModalOpen(false);
  
  // Refresh data
  fetchTemplates();
};

const handleError = (error) => {
  // Show error notification
  showError('Erreur', error.message || 'Une erreur est survenue.');
};
```

This comprehensive modal system provides a complete, user-friendly interface for template management with all the modern UX patterns and safety features expected in a professional admin dashboard.
