# Template Download & Streaming Guide

This guide explains how to use the enhanced template download and streaming functionality for admin and collaborator users.

## 🚀 New Features

### Download Options
- **Download Presentation**: Download the main presentation file (PowerPoint, Word, PDF)
- **Download Demo Video**: Download the demonstration video file
- **Stream Presentation**: Preview the presentation file in browser
- **Stream Demo Video**: Preview the demo video in browser

### API Endpoints

```
GET /api/templates/{id}/download/presentation - Admin & Collaborator only
GET /api/templates/{id}/download/video - Admin & Collaborator only
GET /api/templates/{id}/stream/presentation - Admin & Collaborator only
GET /api/templates/{id}/stream/video - Admin & Collaborator only
```

## 📋 Usage Examples

### Using the Service Directly

```typescript
import { TemplateService } from '@/services/templates';

// Download presentation file
const downloadPresentation = async (templateId: number) => {
  try {
    const blob = await TemplateService.downloadPresentation(templateId);
    // File will be automatically downloaded
  } catch (error) {
    console.error('Download failed:', error);
  }
};

// Download demo video
const downloadVideo = async (templateId: number) => {
  try {
    const blob = await TemplateService.downloadVideo(templateId);
    // Video will be automatically downloaded
  } catch (error) {
    console.error('Download failed:', error);
  }
};

// Stream presentation
const streamPresentation = async (templateId: number) => {
  try {
    const streamUrl = await TemplateService.streamPresentation(templateId);
    window.open(streamUrl, '_blank');
  } catch (error) {
    console.error('Streaming failed:', error);
  }
};

// Stream video
const streamVideo = async (templateId: number) => {
  try {
    const streamUrl = await TemplateService.streamVideo(templateId);
    window.open(streamUrl, '_blank');
  } catch (error) {
    console.error('Streaming failed:', error);
  }
};
```

### Using the Hook

```typescript
import { useTemplates } from '@/hooks/useTemplates';

const MyComponent = () => {
  const {
    downloadPresentation,
    downloadVideo,
    streamPresentation,
    streamVideo
  } = useTemplates();

  const handleDownloadPresentation = async (templateId: number) => {
    try {
      await downloadPresentation(templateId, 'my-presentation.pptx');
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleStreamVideo = async (templateId: number) => {
    try {
      const streamUrl = await streamVideo(templateId);
      // Stream URL is returned for custom handling
    } catch (error) {
      console.error('Streaming failed:', error);
    }
  };

  // ... rest of component
};
```

## 🎨 Admin Interface

The admin templates page now includes a dropdown menu for each template with the following options:

### Download Options
- **Télécharger Présentation**: Downloads the main presentation file
- **Télécharger Vidéo Démo**: Downloads the demo video (only shown if video exists)

### Preview Options
- **Prévisualiser Présentation**: Opens presentation in a new tab for preview
- **Prévisualiser Vidéo Démo**: Opens demo video in a new tab for preview

### UI Features
- Hover-activated dropdown menu
- Icons for each action type
- Conditional display (video options only show if demo video exists)
- Dark mode support
- Responsive design

## 🔒 Security & Permissions

### Access Control
- **Admin**: Full access to all download and streaming functions
- **Collaborator**: Full access to all download and streaming functions
- **Regular Users**: No access to these endpoints

### Error Handling
- **403 Forbidden**: User doesn't have required permissions
- **404 Not Found**: Template or file doesn't exist
- **Network Errors**: Connection issues with the API server

## 📱 File Handling

### Automatic Downloads
- Files are automatically downloaded with appropriate filenames
- Presentation files use the template title + file extension
- Demo videos use the template title + "_demo.mp4"

### Filename Conventions
```
Presentation: "Template Name_presentation.pptx"
Demo Video: "Template Name_demo.mp4"
```

### Supported File Types
- **Presentations**: .pptx, .ppt, .docx, .doc, .pdf
- **Videos**: .mp4, .avi, .mov, .wmv

## 🛠️ Technical Implementation

### Service Layer
The `TemplateService` class includes four new methods:
- `downloadPresentation(id: number): Promise<Blob>`
- `downloadVideo(id: number): Promise<Blob>`
- `streamPresentation(id: number): Promise<string>`
- `streamVideo(id: number): Promise<string>`

### Hook Integration
The `useTemplates` hook provides convenient wrapper functions with:
- Automatic filename generation
- Error handling
- Loading states
- Success notifications

### Component Integration
The admin templates page includes:
- Dropdown menu component
- Conditional rendering based on file availability
- Toast notifications for user feedback
- Responsive design for mobile devices

## 🔧 Troubleshooting

### Common Issues

1. **Download doesn't start**
   - Check user permissions (Admin/Collaborator required)
   - Verify template exists and has associated files
   - Check browser popup blocker settings

2. **Streaming fails**
   - Ensure API server is running
   - Check network connectivity
   - Verify file exists on server

3. **File not found errors**
   - Template may not have associated presentation/video files
   - Files may have been moved or deleted on server
   - Check template data integrity

### Debug Tips
- Check browser console for detailed error messages
- Verify API endpoints are accessible
- Test with different templates to isolate issues
- Check server logs for backend errors
