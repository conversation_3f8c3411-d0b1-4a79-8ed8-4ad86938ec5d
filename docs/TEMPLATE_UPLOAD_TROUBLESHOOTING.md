# Template Upload Troubleshooting Guide

## Current Issue: "Template file is required"

You're getting this error even though a file is selected, which suggests the file isn't reaching the backend properly.

## Debugging Steps

### 1. Use the Debug Page

Navigate to `/debug/template-upload` in your application to access the debugging interface.

### 2. Check Browser Console

With the debugging code added, you should see detailed logs in the browser console:

```
🔍 Debug - File selection: { file details }
🔍 Debug - Form submission: { form data }
🔍 Debug - Template Data: { template data being sent }
🔍 Debug - FormData contents: { what's in FormData }
🚀 Making API request to /templates
🔍 Request headers: { actual headers }
🔍 FormData entries: { actual FormData entries }
```

### 3. Check Network Tab

1. Open browser DevTools (F12)
2. Go to Network tab
3. Try uploading a file
4. Look for the POST request to `/templates`
5. Check:
   - Request headers (should include `Content-Type: multipart/form-data; boundary=...`)
   - Request payload (should show the file and form fields)

### 4. Common Issues and Solutions

#### Issue 1: File Not Being Selected
**Symptoms**: <PERSON>sol<PERSON> shows "NO FILE SELECTED"
**Solution**: Check file input validation and ensure file meets criteria

#### Issue 2: File Lost During FormData Creation
**Symptoms**: File shows in form but not in FormData
**Solution**: Check FormData.append() calls

#### Issue 3: Backend Field Name Mismatch
**Symptoms**: File reaches backend but validation fails
**Solutions**: Try different field names:
```typescript
// Current
formData.append('file', templateData.file);

// Alternatives to try
formData.append('template_file', templateData.file);
formData.append('upload', templateData.file);
formData.append('document', templateData.file);
```

#### Issue 4: Authentication Problems
**Symptoms**: 401 Unauthorized or 403 Forbidden
**Solution**: Check auth token in localStorage

#### Issue 5: CORS Issues
**Symptoms**: Network errors or blocked requests
**Solution**: Ensure backend CORS is configured for file uploads

### 5. Backend Validation Check

The Laravel backend might expect:
- Specific field names
- Specific file types
- Maximum file sizes
- Additional validation rules

### 6. Manual Testing with curl

Test the backend directly:
```bash
curl -X POST http://localhost:8000/api/v1/templates \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=Test Template" \
  -F "description=Test Description" \
  -F "type=ppt" \
  -F "is_active=1" \
  -F "file=@/path/to/test.pptx"
```

### 7. Alternative Field Names to Try

If the current approach doesn't work, try these field names in the service:

```typescript
// In src/services/templates.ts, try one at a time:
formData.append('template_file', templateData.file);  // Option 1
formData.append('upload', templateData.file);         // Option 2
formData.append('document', templateData.file);       // Option 3
formData.append('attachment', templateData.file);     // Option 4
```

### 8. Check Laravel Backend Logs

Look at your Laravel application logs:
```bash
tail -f storage/logs/laravel.log
```

### 9. Temporary Workaround

If the issue persists, try this simplified version:

```typescript
// Minimal FormData approach
const formData = new FormData();
formData.append('name', 'Test Template');
formData.append('description', 'Test Description');
formData.append('type', 'ppt');
formData.append('is_active', '1');
formData.append('file', selectedFile);

// Direct axios call without interceptors
const response = await axios.post('http://localhost:8000/api/v1/templates', formData, {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
  }
});
```

## Next Steps

1. **Run the debug page** at `/debug/template-upload`
2. **Check console logs** for detailed information
3. **Examine network requests** in DevTools
4. **Try alternative field names** if needed
5. **Check backend logs** for server-side errors

## Expected Debug Output

When working correctly, you should see:
```
✅ File validation passed, setting selected file
🚀 Submitting template data: { valid template data }
🔍 Debug - Template Data: { file details present }
🔍 FormData entries:
  name: Test Template
  description: Test Description  
  type: ppt
  is_active: 1
  file: File(test.pptx, 1234567 bytes, application/vnd.openxmlformats-officedocument.presentationml.presentation)
```

## Contact Information

If the issue persists after trying these steps, provide:
1. Console log output
2. Network request details
3. Backend error logs
4. File type and size being uploaded

This will help identify the exact cause of the "Template file is required" error.
