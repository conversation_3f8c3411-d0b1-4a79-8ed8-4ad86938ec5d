# Template API Implementation Guide

This guide explains how to use the Template API service in the Sademy dashboard admin with axios and best practices.

## 📁 File Structure

```
src/
├── services/
│   └── templates.ts          # Template API service
├── hooks/
│   └── useTemplates.ts       # React hooks for template management
├── app/admin/templates/
│   └── page.tsx             # Admin templates management page
└── components/Admin/Templates/
    └── TemplateExample.tsx  # Example usage component
```

## 🚀 Quick Start

### 1. Import the Template Service

```typescript
import { TemplateService } from '@/services/templates';
```

### 2. Use the React Hooks (Recommended)

```typescript
import { useTemplates, useTemplateUpload } from '@/hooks/useTemplates';

const MyComponent = () => {
  const { templates, loading, error, fetchTemplates } = useTemplates();
  const { uploadTemplate, uploading, uploadProgress } = useTemplateUpload();
  
  // Your component logic here
};
```

## 📡 API Endpoints

### GET /api/v1/templates
Fetch all templates with optional filters.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `type` (string): Filter by template type (ppt, doc, pdf)
- `is_active` (boolean): Filter by active status
- `search` (string): Search in template names and descriptions
- `page` (number): Page number for pagination
- `per_page` (number): Number of items per page

**Example Usage:**
```typescript
// Using the service directly
const templates = await TemplateService.getTemplates({
  type: 'ppt',
  is_active: true,
  search: 'business',
  page: 1,
  per_page: 10
});

// Using the hook
const { templates, loading, error } = useTemplates({
  type: 'ppt',
  is_active: true
});
```

### POST /api/v1/templates
Create a new template (Admin only).

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: multipart/form-data
```

**Form Data:**
- `name` (string, required): Template name
- `description` (string, required): Template description
- `type` (string, required): Template type (ppt, doc, pdf)
- `file` (File, required): Template file
- `demo_video` (File, optional): Demo video file
- `is_active` (boolean, required): Active status

**Example Usage:**
```typescript
// Using the service directly
const templateData = {
  name: 'Business Presentation',
  description: 'Professional business template',
  type: 'ppt',
  file: selectedFile,
  demo_video: selectedVideo, // optional
  is_active: true
};

const result = await TemplateService.createTemplate(templateData);

// Using the upload hook (recommended for progress tracking)
const { uploadTemplate, uploading, uploadProgress } = useTemplateUpload();

const handleUpload = async () => {
  try {
    const newTemplate = await uploadTemplate(templateData);
    console.log('Template created:', newTemplate);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

## 🎯 Best Practices

### 1. Error Handling

Always wrap API calls in try-catch blocks and provide user-friendly error messages:

```typescript
const { templates, error, clearError } = useTemplates();

// Display errors to users
{error && (
  <div className="error-message">
    <p>{error}</p>
    <button onClick={clearError}>Dismiss</button>
  </div>
)}
```

### 2. Loading States

Show loading indicators during API operations:

```typescript
const { loading } = useTemplates();
const { uploading, uploadProgress } = useTemplateUpload();

// Loading indicator
{loading && <div>Loading templates...</div>}

// Upload progress
{uploading && (
  <div className="progress-bar">
    <div style={{ width: `${uploadProgress}%` }} />
    <span>{uploadProgress}%</span>
  </div>
)}
```

### 3. File Validation

Validate files before upload:

```typescript
const validateFile = (file: File, type: string) => {
  const maxSize = 50 * 1024 * 1024; // 50MB
  const allowedTypes = {
    ppt: ['.pptx', '.ppt'],
    doc: ['.docx', '.doc'],
    pdf: ['.pdf']
  };

  if (file.size > maxSize) {
    throw new Error('File size must be less than 50MB');
  }

  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  if (!allowedTypes[type]?.includes(extension)) {
    throw new Error(`Invalid file type for ${type} template`);
  }
};
```

### 4. Authentication Check

Always verify admin permissions before allowing template management:

```typescript
import { AuthService } from '@/services/auth';

const isAdmin = AuthService.isAdmin();
if (!isAdmin) {
  // Redirect or show access denied message
  return <div>Access denied. Admin privileges required.</div>;
}
```

## 🔧 Advanced Usage

### Custom Filters and Pagination

```typescript
const [filters, setFilters] = useState({
  type: 'ppt',
  is_active: true,
  page: 1,
  per_page: 20
});

const { templates, pagination, fetchTemplates } = useTemplates(filters);

// Update filters
const handleFilterChange = (newFilters) => {
  const updatedFilters = { ...filters, ...newFilters, page: 1 };
  setFilters(updatedFilters);
  fetchTemplates(updatedFilters);
};

// Pagination
const handlePageChange = (page) => {
  const updatedFilters = { ...filters, page };
  setFilters(updatedFilters);
  fetchTemplates(updatedFilters);
};
```

### File Download

```typescript
const { downloadTemplate } = useTemplates();

const handleDownload = async (templateId, filename) => {
  try {
    await downloadTemplate(templateId, filename);
    // File will be automatically downloaded
  } catch (error) {
    console.error('Download failed:', error);
  }
};
```

### Template Status Toggle

```typescript
const { toggleTemplateStatus } = useTemplates();

const handleToggleStatus = async (templateId) => {
  try {
    await toggleTemplateStatus(templateId);
    // Template status updated in the list automatically
  } catch (error) {
    console.error('Status toggle failed:', error);
  }
};
```

## 🛡️ Security Considerations

1. **Authentication**: All API calls automatically include the Bearer token from localStorage
2. **Admin Only**: Create, update, and delete operations require admin role
3. **File Validation**: Validate file types and sizes on both client and server
4. **CSRF Protection**: Use the configured axios instance which handles CSRF tokens
5. **Error Handling**: Never expose sensitive error details to users

## 📱 Responsive Design

The admin templates page is fully responsive and includes:
- Mobile-friendly table with horizontal scroll
- Responsive pagination controls
- Touch-friendly buttons and interactions
- Dark mode support

## 🎨 UI Components

The implementation includes:
- **Data Table**: Sortable, filterable templates list
- **Modal Forms**: Create and edit template modals
- **Progress Indicators**: Upload progress and loading states
- **Error Messages**: User-friendly error display
- **Pagination**: Navigate through large datasets
- **Search and Filters**: Find specific templates quickly

## 🔄 State Management

The hooks provide automatic state management:
- **Optimistic Updates**: UI updates immediately for better UX
- **Error Recovery**: Automatic rollback on failed operations
- **Cache Management**: Efficient data fetching and caching
- **Real-time Updates**: Automatic refresh after mutations

## 📊 Example Implementation

See `src/components/Admin/Templates/TemplateExample.tsx` for a complete working example that demonstrates:
- Template listing with filters
- File upload with progress tracking
- Error handling and loading states
- Form validation and submission
- Responsive design patterns

This implementation follows all the best practices mentioned in your memory and provides a robust, scalable solution for template management in the admin dashboard.
